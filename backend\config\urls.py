from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from apps.api_docs import api_documentation
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularSwaggerView,
    SpectacularRedocView,
)

urlpatterns = [
    path("admin/", admin.site.urls),
    # API文档
    path("api/schema/", SpectacularAPIView.as_view(), name="schema"),
    path(
        "api/docs/",
        SpectacularSwaggerView.as_view(url_name="schema"),
        name="swagger-ui",
    ),
    path("api/redoc/", SpectacularRedocView.as_view(url_name="schema"), name="redoc"),
    path("api/docs/legacy/", api_documentation, name="api_documentation"),
    # API路由
    path("api/auth/", include("apps.users.urls")),
    path("api/users/", include("apps.users.urls")),  # 用户管理API
    path("api/passwords/", include("apps.passwords.urls")),
    path("api/categories/", include("apps.categories.urls")),
    path("api/sharing/", include("apps.sharing.urls")),
    path("api/audit/", include("apps.audit.urls")),
    path("api/system/", include("apps.system.urls")),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
