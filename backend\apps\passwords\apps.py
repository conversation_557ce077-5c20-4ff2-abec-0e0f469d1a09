from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class PasswordsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.passwords'
    verbose_name = _("密码管理")
    
    def ready(self):
        """应用准备就绪时的初始化操作"""
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
        
        # 注册密码强度检查器
        try:
            from .utils import register_password_validators
            register_password_validators()
        except ImportError:
            pass