#!/usr/bin/env python
"""
测试密码组功能的脚本
"""
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordEntryGroup, GroupPermission, PasswordEntry

User = get_user_model()

def test_group_api():
    """测试组管理API"""
    base_url = "http://localhost:8001/api/passwords"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试密码组管理功能 ===")
    
    # 1. 测试创建组
    print("\n1. 测试创建组")
    group_data = {
        "name": "开发团队",
        "description": "开发团队的密码组"
    }
    
    response = requests.post(f"{base_url}/groups/", json=group_data, headers=headers)
    print(f"创建组响应: {response.status_code}")
    if response.status_code == 201:
        group = response.json()
        print(f"创建的组: {group}")
        group_id = group['id']
    else:
        print(f"创建组失败: {response.text}")
        return
    
    # 2. 测试获取组列表
    print("\n2. 测试获取组列表")
    response = requests.get(f"{base_url}/groups/", headers=headers)
    print(f"获取组列表响应: {response.status_code}")
    if response.status_code == 200:
        groups = response.json()
        print(f"组列表: {json.dumps(groups, indent=2, ensure_ascii=False)}")
    else:
        print(f"获取组列表失败: {response.text}")
    
    # 3. 测试获取组详情
    print(f"\n3. 测试获取组详情 (ID: {group_id})")
    response = requests.get(f"{base_url}/groups/{group_id}/", headers=headers)
    print(f"获取组详情响应: {response.status_code}")
    if response.status_code == 200:
        group_detail = response.json()
        print(f"组详情: {json.dumps(group_detail, indent=2, ensure_ascii=False)}")
    else:
        print(f"获取组详情失败: {response.text}")
    
    # 4. 测试获取组权限列表
    print("\n4. 测试获取组权限列表")
    response = requests.get(f"{base_url}/group-permissions/", headers=headers)
    print(f"获取组权限列表响应: {response.status_code}")
    if response.status_code == 200:
        permissions = response.json()
        print(f"组权限列表: {json.dumps(permissions, indent=2, ensure_ascii=False)}")
    else:
        print(f"获取组权限列表失败: {response.text}")
    
    # 5. 测试获取组成员关系列表
    print("\n5. 测试获取组成员关系列表")
    response = requests.get(f"{base_url}/group-memberships/", headers=headers)
    print(f"获取组成员关系列表响应: {response.status_code}")
    if response.status_code == 200:
        memberships = response.json()
        print(f"组成员关系列表: {json.dumps(memberships, indent=2, ensure_ascii=False)}")
    else:
        print(f"获取组成员关系列表失败: {response.text}")
    
    print("\n=== 测试完成 ===")

def test_database_models():
    """测试数据库模型"""
    print("\n=== 测试数据库模型 ===")
    
    try:
        # 获取admin用户
        admin_user = User.objects.get(username='admin')
        print(f"找到用户: {admin_user.username}")
        
        # 创建测试组
        group, created = PasswordEntryGroup.objects.get_or_create(
            name="测试组",
            created_by=admin_user,
            defaults={
                "description": "这是一个测试组"
            }
        )
        
        if created:
            print(f"创建了新组: {group.name}")
        else:
            print(f"组已存在: {group.name}")
        
        # 测试组方法
        print(f"组成员数量: {group.get_members_count()}")
        print(f"组密码条目数量: {group.get_password_entries_count()}")
        
        # 创建组权限
        permission, created = GroupPermission.objects.get_or_create(
            user=admin_user,
            group=group,
            defaults={
                "permission": "admin",
                "granted_by": admin_user
            }
        )
        
        if created:
            print(f"创建了新权限: {permission}")
        else:
            print(f"权限已存在: {permission}")
        
        # 测试权限检查
        has_admin = group.has_permission(admin_user, 'admin')
        has_view = group.has_permission(admin_user, 'view')
        print(f"用户是否有管理员权限: {has_admin}")
        print(f"用户是否有查看权限: {has_view}")
        
        print("数据库模型测试成功!")
        
    except Exception as e:
        print(f"数据库模型测试失败: {e}")

if __name__ == "__main__":
    print("开始测试密码组功能...")
    
    # 测试数据库模型
    test_database_models()
    
    # 测试API
    test_group_api()
