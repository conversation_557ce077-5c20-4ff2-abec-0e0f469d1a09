#!/usr/bin/env python
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.sharing.models import OneTimeLink
from apps.passwords.models import PasswordEntry
from django.contrib.auth import get_user_model
from django.utils import timezone
import secrets

User = get_user_model()

def check_and_create_test_data():
    print("检查分享链接数据...")
    
    # 检查现有的分享链接
    links = OneTimeLink.objects.all()
    print(f"现有分享链接数量: {links.count()}")
    
    if links.exists():
        for link in links:
            print(f"- Token: {link.token}")
            print(f"  密码条目: {link.password_entry.title}")
            print(f"  状态: {link.status}")
            print(f"  过期时间: {link.expires_at}")
            print(f"  是否有效: {link.is_valid}")
    else:
        print("没有分享链接，创建测试数据...")
        
        # 获取第一个用户
        user = User.objects.first()
        if not user:
            print("没有用户，创建测试用户...")
            user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
        
        # 获取第一个密码条目
        password_entry = PasswordEntry.objects.first()
        if not password_entry:
            print("没有密码条目，创建测试密码条目...")
            password_entry = PasswordEntry.objects.create(
                title='测试密码',
                username='testuser',
                password='testpass123',
                url='https://example.com',
                created_by=user
            )
        
        # 创建测试分享链接
        token = secrets.token_urlsafe(8)
        expires_at = timezone.now() + timedelta(days=7)
        
        link = OneTimeLink.objects.create(
            token=token,
            password_entry=password_entry,
            created_by=user,
            expires_at=expires_at,
            status='active'
        )
        
        print(f"创建了测试分享链接:")
        print(f"- Token: {link.token}")
        print(f"- 密码条目: {link.password_entry.title}")
        print(f"- 过期时间: {link.expires_at}")
        print(f"- 访问URL: http://localhost:5666/share/{link.token}")

if __name__ == '__main__':
    check_and_create_test_data()
