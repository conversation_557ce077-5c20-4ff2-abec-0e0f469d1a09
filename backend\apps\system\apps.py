from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class SystemConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.system'
    verbose_name = _("系统管理")
    
    def ready(self):
        """应用准备就绪时的初始化操作"""
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
        
        # 初始化默认系统设置
        try:
            from .utils import initialize_default_settings
            initialize_default_settings()
        except ImportError:
            pass
        
        # 启动系统监控任务
        try:
            from .tasks import start_system_monitor
            start_system_monitor()
        except ImportError:
            pass
        
        # 启动自动备份任务
        try:
            from .tasks import start_backup_scheduler
            start_backup_scheduler()
        except ImportError:
            pass