#!/usr/bin/env python
"""
测试API端点是否正常工作
"""
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


def get_auth_token():
    """获取认证token"""
    try:
        # 获取或创建测试用户
        user, created = User.objects.get_or_create(
            username="admin",
            defaults={
                "email": "<EMAIL>",
                "is_staff": True,
                "is_superuser": True,
            },
        )

        if created:
            user.set_password("admin123")
            user.save()
            print(f"创建测试用户: {user.username}")

        # 生成JWT token
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)

        return access_token
    except Exception as e:
        print(f"获取认证token失败: {e}")
        return None


def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:8001/api"

    # 获取认证token
    token = get_auth_token()
    if not token:
        print("无法获取认证token，退出测试")
        return

    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}

    # 测试端点列表
    endpoints = [
        "/passwords/system-types/",
        "/passwords/environments/",
        "/passwords/database-types/",
        "/passwords/protocols/",
        "/passwords/stats/",
        "/passwords/categories/",
        "/passwords/passwords/",
    ]

    print("开始测试API端点...")
    print("=" * 50)

    for endpoint in endpoints:
        url = base_url + endpoint
        try:
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                print(f"✓ {endpoint}")
                print(f"  状态码: {response.status_code}")
                print(
                    f"  数据: {json.dumps(data, ensure_ascii=False, indent=2)[:200]}..."
                )
            else:
                print(f"✗ {endpoint}")
                print(f"  状态码: {response.status_code}")
                print(f"  错误: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"✗ {endpoint}")
            print(f"  连接错误: {e}")

        print("-" * 30)

    print("API端点测试完成")


if __name__ == "__main__":
    test_api_endpoints()
