#!/usr/bin/env python
"""
测试分页功能的脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from apps.passwords.views import PasswordEntryListCreateView
from apps.passwords.models import PasswordEntry, Category
from utils.encryption import encrypt_data

User = get_user_model()


def test_pagination():
    """测试分页功能"""
    print("=== 测试分页功能 ===")

    # 创建测试用户
    user, created = User.objects.get_or_create(
        email="<EMAIL>",
        defaults={"username": "testuser", "first_name": "Test", "last_name": "User"},
    )

    # 创建测试分类
    category, created = Category.objects.get_or_create(
        name="测试分类", defaults={"user": user}
    )

    # 创建测试密码条目（如果不存在）
    existing_count = PasswordEntry.objects.filter(owner=user).count()
    print(f"现有密码条目数量: {existing_count}")

    if existing_count < 30:
        print("创建测试密码条目...")
        for i in range(30 - existing_count):
            PasswordEntry.objects.create(
                title=f"测试密码{i+existing_count+1}",
                username=f"user{i+existing_count+1}",
                password=encrypt_data(f"password{i+existing_count+1}"),
                url=f"https://example{i+existing_count+1}.com",
                hostname=f"host{i+existing_count+1}",
                category=category,
                owner=user,
            )
        print(f"创建了 {30 - existing_count} 个测试密码条目")

    # 创建请求工厂
    factory = RequestFactory()

    # 测试默认分页（page_size=20）
    print("\n1. 测试默认分页 (page_size=20)")
    request = factory.get("/api/passwords/?page=1")
    request.user = user
    # 添加query_params属性
    from django.http import QueryDict

    request.query_params = QueryDict("page=1")

    view = PasswordEntryListCreateView()
    view.setup(request)

    queryset = view.get_queryset()
    paginator = view.pagination_class()
    page = paginator.paginate_queryset(queryset, request, view=view)

    print(f"查询集总数: {queryset.count()}")
    print(f"分页后数量: {len(page) if page else 0}")
    print(f"分页器页面大小: {paginator.page_size}")

    # 测试自定义分页大小（page_size=10）
    print("\n2. 测试自定义分页 (page_size=10)")
    request = factory.get("/api/passwords/?page=1&page_size=10")
    request.user = user
    request.query_params = QueryDict("page=1&page_size=10")

    view = PasswordEntryListCreateView()
    view.setup(request)

    queryset = view.get_queryset()
    paginator = view.pagination_class()

    # 调试信息
    print(f"请求参数: {request.query_params}")
    print(f"page_size参数: {request.query_params.get('page_size')}")
    print(f"分页器初始页面大小: {paginator.page_size}")

    page = paginator.paginate_queryset(queryset, request, view=view)

    print(f"分页后页面大小: {paginator.page_size}")
    print(f"查询集总数: {queryset.count()}")
    print(f"分页后数量: {len(page) if page else 0}")

    # 测试自定义分页大小（page_size=50）
    print("\n3. 测试自定义分页 (page_size=50)")
    request = factory.get("/api/passwords/?page=1&page_size=50")
    request.user = user
    request.query_params = QueryDict("page=1&page_size=50")

    view = PasswordEntryListCreateView()
    view.setup(request)

    queryset = view.get_queryset()
    paginator = view.pagination_class()
    page = paginator.paginate_queryset(queryset, request, view=view)

    print(f"查询集总数: {queryset.count()}")
    print(f"分页后数量: {len(page) if page else 0}")
    print(f"分页器页面大小: {paginator.page_size}")

    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    test_pagination()
