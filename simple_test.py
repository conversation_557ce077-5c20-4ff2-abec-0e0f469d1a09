#!/usr/bin/env python
"""
简化的测试脚本
用于验证项目结构和基本功能
"""

import os
import sys
from pathlib import Path

def check_project_structure():
    """检查项目结构"""
    print("检查项目结构...")
    
    base_dir = Path('.')
    backend_dir = base_dir / 'backend'
    
    required_files = [
        'backend/manage.py',
        'backend/config/settings.py',
        'backend/config/urls.py',
        'backend/apps/users/models.py',
        'backend/apps/passwords/models.py',
        'backend/requirements.txt',
        'docker-compose.yml'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (base_dir / file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print("\n缺失的文件:")
        for file_path in missing_files:
            print(f"✗ {file_path}")
        return False
    
    print("\n✓ 项目结构完整")
    return True

def check_django_config():
    """检查Django配置"""
    print("\n检查Django配置...")
    
    try:
        # 添加backend目录到Python路径
        backend_path = os.path.join(os.getcwd(), 'backend')
        if backend_path not in sys.path:
            sys.path.insert(0, backend_path)
        
        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
        
        import django
        from django.conf import settings
        
        django.setup()
        
        print(f"✓ Django版本: {django.get_version()}")
        print(f"✓ 数据库引擎: {settings.DATABASES['default']['ENGINE']}")
        print(f"✓ 已安装应用数量: {len(settings.INSTALLED_APPS)}")
        
        # 检查自定义应用
        custom_apps = [app for app in settings.INSTALLED_APPS if app.startswith('apps.')]
        print(f"✓ 自定义应用: {', '.join(custom_apps)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Django配置检查失败: {e}")
        return False

def check_models():
    """检查模型定义"""
    print("\n检查模型定义...")
    
    try:
        from apps.users.models import User
        from apps.passwords.models import Password
        from apps.categories.models import Category
        
        print("✓ User模型")
        print("✓ Password模型")
        print("✓ Category模型")
        
        # 检查模型字段
        user_fields = [f.name for f in User._meta.fields]
        password_fields = [f.name for f in Password._meta.fields]
        
        print(f"✓ User字段数量: {len(user_fields)}")
        print(f"✓ Password字段数量: {len(password_fields)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型检查失败: {e}")
        return False

def check_urls():
    """检查URL配置"""
    print("\n检查URL配置...")
    
    try:
        from django.urls import reverse
        from config.urls import urlpatterns
        
        print(f"✓ 主URL配置包含 {len(urlpatterns)} 个路由")
        
        # 尝试解析一些基本URL
        try:
            admin_url = reverse('admin:index')
            print(f"✓ 管理后台URL: {admin_url}")
        except:
            print("⚠ 管理后台URL未配置")
        
        return True
        
    except Exception as e:
        print(f"✗ URL配置检查失败: {e}")
        return False

def generate_report():
    """生成测试报告"""
    print("\n" + "=" * 50)
    print("项目验证报告")
    print("=" * 50)
    
    tests = [
        ('项目结构', check_project_structure),
        ('Django配置', check_django_config),
        ('模型定义', check_models),
        ('URL配置', check_urls)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 统计结果
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("测试结果统计")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 通过")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！项目配置正确！")
        return True
    else:
        print("\n❌ 部分测试失败，请检查配置")
        return False

def main():
    """主函数"""
    print("密码管理系统 - 项目验证")
    print("=" * 50)
    
    try:
        success = generate_report()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()