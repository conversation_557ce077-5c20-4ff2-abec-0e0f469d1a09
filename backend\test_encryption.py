#!/usr/bin/env python
"""
测试加密解密功能的脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from utils.encryption import encrypt_data, decrypt_data

def test_encryption():
    """测试加密解密功能"""
    print("=== 测试加密解密功能 ===")
    
    test_passwords = [
        "TestPassword123!",
        "simple",
        "a",
        "very_long_password_with_many_characters_1234567890",
        "密码中文测试",
        "!@#$%^&*()_+-=[]{}|;:,.<>?",
    ]
    
    for i, password in enumerate(test_passwords, 1):
        print(f"\n{i}. 测试密码: '{password}'")
        print(f"   原始长度: {len(password)}")
        
        try:
            # 加密
            encrypted = encrypt_data(password)
            print(f"   加密成功，长度: {len(encrypted)}")
            print(f"   加密结果: {encrypted[:50]}...")
            
            # 解密
            decrypted = decrypt_data(encrypted)
            print(f"   解密成功，长度: {len(decrypted)}")
            print(f"   解密结果: '{decrypted}'")
            
            # 验证
            if password == decrypted:
                print(f"   ✅ 加密解密匹配")
            else:
                print(f"   ❌ 加密解密不匹配")
                print(f"   期望: '{password}'")
                print(f"   实际: '{decrypted}'")
                
        except Exception as e:
            print(f"   ❌ 加密解密失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_encryption()
