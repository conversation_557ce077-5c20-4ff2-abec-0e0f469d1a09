#!/usr/bin/env python
"""
测试用户管理API的脚本
"""
import requests
import json


def test_user_management_api():
    """测试用户管理API"""
    base_url = "http://localhost:8001/api/users"

    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}

    login_response = requests.post(
        "http://localhost:8001/api/auth/login/", json=login_data
    )
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return

    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}

    print("=== 测试用户管理API ===")

    # 1. 测试获取用户列表
    print("\n1. 测试获取用户列表")
    response = requests.get(f"{base_url}/users/", headers=headers)
    print(f"获取用户列表响应: {response.status_code}")
    if response.status_code == 200:
        users = response.json()
        print(f"用户数量: {users.get('count', 0)}")
        if users.get("results"):
            for user in users["results"][:3]:  # 只显示前3个用户
                print(
                    f"- {user['username']} ({user['email']}) - {'激活' if user['is_active'] else '禁用'}"
                )
    else:
        print(f"获取用户列表失败: {response.text}")

    # 2. 测试获取角色列表
    print("\n2. 测试获取角色列表")
    response = requests.get(f"{base_url}/roles/", headers=headers)
    print(f"获取角色列表响应: {response.status_code}")
    if response.status_code == 200:
        roles = response.json()
        print(f"角色数量: {roles.get('count', 0)}")
        if roles.get("results"):
            for role in roles["results"]:
                print(f"- {role['name']}: {role['description']}")
                print(f"  权限数量: {len(role.get('permissions', []))}")
    else:
        print(f"获取角色列表失败: {response.text}")

    # 3. 测试获取部门列表
    print("\n3. 测试获取部门列表")
    response = requests.get(f"{base_url}/departments/", headers=headers)
    print(f"获取部门列表响应: {response.status_code}")
    if response.status_code == 200:
        departments_data = response.json()
        if isinstance(departments_data, dict) and "results" in departments_data:
            departments = departments_data["results"]
            print(f"部门数量: {departments_data.get('count', len(departments))}")
        else:
            departments = departments_data
            print(f"部门数量: {len(departments)}")

        for dept in departments:
            if isinstance(dept, dict):
                print(f"- {dept.get('name', 'N/A')}: {dept.get('description', 'N/A')}")
            else:
                print(f"- {dept}")
    else:
        print(f"获取部门列表失败: {response.text}")

    # 4. 测试获取团队列表
    print("\n4. 测试获取团队列表")
    response = requests.get(f"{base_url}/teams/", headers=headers)
    print(f"获取团队列表响应: {response.status_code}")
    if response.status_code == 200:
        teams_data = response.json()
        if isinstance(teams_data, dict) and "results" in teams_data:
            teams = teams_data["results"]
            print(f"团队数量: {teams_data.get('count', len(teams))}")
        else:
            teams = teams_data
            print(f"团队数量: {len(teams)}")

        for team in teams:
            if isinstance(team, dict):
                print(f"- {team.get('name', 'N/A')}: {team.get('description', 'N/A')}")
            else:
                print(f"- {team}")
    else:
        print(f"获取团队列表失败: {response.text}")

    # 5. 测试创建角色
    print("\n5. 测试创建角色")
    role_data = {
        "name": "manager",
        "description": "Department manager role for testing",
        "permissions": ["users.view", "passwords.view"],
    }

    response = requests.post(f"{base_url}/roles/", json=role_data, headers=headers)
    print(f"创建角色响应: {response.status_code}")
    if response.status_code == 201:
        role = response.json()
        print(f"创建的角色: {role['name']}")
        role_id = role["id"]

        # 6. 测试获取角色详情
        print(f"\n6. 测试获取角色详情 (ID: {role_id})")
        response = requests.get(f"{base_url}/roles/{role_id}/", headers=headers)
        print(f"获取角色详情响应: {response.status_code}")
        if response.status_code == 200:
            role_detail = response.json()
            print(f"角色详情: {role_detail['name']} - {role_detail['description']}")
            print(f"权限: {role_detail.get('permissions', [])}")

        # 7. 测试更新角色
        print(f"\n7. 测试更新角色 (ID: {role_id})")
        update_data = {
            "description": "更新后的角色描述",
            "permissions": ["users.view", "passwords.view", "passwords.create"],
        }
        response = requests.put(
            f"{base_url}/roles/{role_id}/", json=update_data, headers=headers
        )
        print(f"更新角色响应: {response.status_code}")
        if response.status_code == 200:
            updated_role = response.json()
            print(f"更新后的角色: {updated_role['description']}")
            print(f"更新后的权限: {updated_role.get('permissions', [])}")

        # 8. 测试删除角色
        print(f"\n8. 测试删除角色 (ID: {role_id})")
        response = requests.delete(f"{base_url}/roles/{role_id}/", headers=headers)
        print(f"删除角色响应: {response.status_code}")
        if response.status_code == 204:
            print("角色删除成功")
        else:
            print(f"删除角色失败: {response.text}")
    else:
        print(f"创建角色失败: {response.text}")

    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    test_user_management_api()
