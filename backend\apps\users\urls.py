from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .menu_views import MenuListView

# 认证相关URL
auth_urlpatterns = [
    path("login/", views.LoginView.as_view(), name="login"),
    path("logout/", views.LogoutView.as_view(), name="logout"),
    path(
        "token/refresh/", views.CustomTokenRefreshView.as_view(), name="token_refresh"
    ),
    path(
        "password/change/", views.PasswordChangeView.as_view(), name="password_change"
    ),
    path("password/reset/", views.PasswordResetView.as_view(), name="password_reset"),
    path(
        "password/reset/confirm/<str:uidb64>/<str:token>/",
        views.PasswordResetConfirmView.as_view(),
        name="password_reset_confirm",
    ),
    path("mfa/setup/", views.MFASetupView.as_view(), name="mfa_setup"),
    path("mfa/qrcode/", views.MFAQRCodeView.as_view(), name="mfa_qrcode"),
    path("codes/", views.AccessCodesView.as_view(), name="access_codes"),
    path("menu/all/", MenuListView.as_view(), name="menu_list"),
]

# 用户管理相关URL
user_urlpatterns = [
    path("profile/", views.UserProfileView.as_view(), name="user_profile"),
    path("users/", views.UserListCreateView.as_view(), name="user_list_create"),
    path("users/<int:pk>/", views.UserDetailView.as_view(), name="user_detail"),
]

# 组织架构相关URL
org_urlpatterns = [
    path(
        "departments/",
        views.DepartmentListCreateView.as_view(),
        name="department_list_create",
    ),
    path(
        "departments/<int:pk>/",
        views.DepartmentDetailView.as_view(),
        name="department_detail",
    ),
    path("teams/", views.TeamListCreateView.as_view(), name="team_list_create"),
    path("teams/<int:pk>/", views.TeamDetailView.as_view(), name="team_detail"),
    path("roles/", views.RoleListCreateView.as_view(), name="role_list_create"),
    path("roles/<int:pk>/", views.RoleDetailView.as_view(), name="role_detail"),
]

urlpatterns = [
    path("", include(auth_urlpatterns)),
    path("", include(user_urlpatterns)),
    path("", include(org_urlpatterns)),
]
