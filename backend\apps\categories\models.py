from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

User = get_user_model()


class CategoryTemplate(models.Model):
    """分类模板模型"""

    name = models.CharField(max_length=100, verbose_name=_("模板名称"))
    description = models.TextField(blank=True, verbose_name=_("模板描述"))
    icon = models.CharField(max_length=50, blank=True, verbose_name=_("图标"))
    color = models.CharField(max_length=7, default="#1890ff", verbose_name=_("颜色"))

    # 预定义字段
    default_fields = models.JSONField(
        default=list, verbose_name=_("默认字段"), help_text=_("预定义的自定义字段配置")
    )

    # 系统模板
    is_system = models.BooleanField(default=False, verbose_name=_("系统模板"))
    is_active = models.<PERSON><PERSON>anField(default=True, verbose_name=_("启用状态"))

    # 使用统计
    usage_count = models.IntegerField(default=0, verbose_name=_("使用次数"))

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("分类模板")
        verbose_name_plural = _("分类模板")
        db_table = "category_templates"
        ordering = ["name"]

    def __str__(self):
        return self.name

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=["usage_count"])


class CategoryGroup(models.Model):
    """分类组模型"""

    name = models.CharField(max_length=100, verbose_name=_("组名称"))
    description = models.TextField(blank=True, verbose_name=_("组描述"))
    icon = models.CharField(max_length=50, blank=True, verbose_name=_("图标"))
    color = models.CharField(max_length=7, default="#722ed1", verbose_name=_("颜色"))

    # 所有者
    owner = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("所有者"))

    # 共享设置
    is_shared = models.BooleanField(default=False, verbose_name=_("共享组"))
    shared_with = models.ManyToManyField(
        User,
        through="CategoryGroupMember",
        related_name="shared_category_groups",
        blank=True,
        verbose_name=_("共享用户"),
    )

    # 排序
    order = models.IntegerField(default=0, verbose_name=_("排序"))

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("分类组")
        verbose_name_plural = _("分类组")
        db_table = "category_groups"
        ordering = ["order", "name"]
        unique_together = ["name", "owner"]

    def __str__(self):
        return self.name


class CategoryGroupMember(models.Model):
    """分类组成员模型"""

    PERMISSION_CHOICES = [
        ("view", _("查看")),
        ("edit", _("编辑")),
        ("admin", _("管理")),
    ]

    group = models.ForeignKey(
        CategoryGroup, on_delete=models.CASCADE, verbose_name=_("分类组")
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("用户"))
    permission = models.CharField(
        max_length=10,
        choices=PERMISSION_CHOICES,
        default="view",
        verbose_name=_("权限"),
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("加入时间"))

    class Meta:
        verbose_name = _("分类组成员")
        verbose_name_plural = _("分类组成员")
        db_table = "category_group_members"
        unique_together = ["group", "user"]

    def __str__(self):
        return f"{self.group.name} - {self.user.username}"


class CategoryRule(models.Model):
    """分类规则模型"""

    RULE_TYPES = [
        ("url_contains", _("URL包含")),
        ("url_domain", _("URL域名")),
        ("title_contains", _("标题包含")),
        ("title_regex", _("标题正则")),
        ("username_contains", _("用户名包含")),
        ("email_domain", _("邮箱域名")),
        ("custom_field", _("自定义字段")),
    ]

    name = models.CharField(max_length=100, verbose_name=_("规则名称"))
    description = models.TextField(blank=True, verbose_name=_("规则描述"))

    # 规则配置
    rule_type = models.CharField(
        max_length=20, choices=RULE_TYPES, verbose_name=_("规则类型")
    )
    rule_value = models.CharField(max_length=500, verbose_name=_("规则值"))
    is_case_sensitive = models.BooleanField(default=False, verbose_name=_("区分大小写"))

    # 目标分类
    target_category = models.ForeignKey(
        "passwords.Category", on_delete=models.CASCADE, verbose_name=_("目标分类")
    )

    # 规则设置
    is_active = models.BooleanField(default=True, verbose_name=_("启用状态"))
    priority = models.IntegerField(default=0, verbose_name=_("优先级"))
    auto_apply = models.BooleanField(default=True, verbose_name=_("自动应用"))

    # 所有者
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, verbose_name=_("创建者")
    )

    # 统计信息
    match_count = models.IntegerField(default=0, verbose_name=_("匹配次数"))
    last_matched = models.DateTimeField(
        null=True, blank=True, verbose_name=_("最后匹配时间")
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("分类规则")
        verbose_name_plural = _("分类规则")
        db_table = "category_rules"
        ordering = ["-priority", "name"]

    def __str__(self):
        return self.name

    def test_match(self, password_entry):
        """测试密码条目是否匹配规则"""
        from django.utils import timezone
        import re

        if not self.is_active:
            return False

        value = self.rule_value
        if not self.is_case_sensitive:
            value = value.lower()

        try:
            if self.rule_type == "url_contains":
                url = (
                    password_entry.url.lower()
                    if not self.is_case_sensitive
                    else password_entry.url
                )
                return value in url

            elif self.rule_type == "url_domain":
                from urllib.parse import urlparse

                domain = urlparse(password_entry.url).netloc.lower()
                return value.lower() in domain

            elif self.rule_type == "title_contains":
                title = (
                    password_entry.title.lower()
                    if not self.is_case_sensitive
                    else password_entry.title
                )
                return value in title

            elif self.rule_type == "title_regex":
                flags = re.IGNORECASE if not self.is_case_sensitive else 0
                return bool(re.search(value, password_entry.title, flags))

            elif self.rule_type == "username_contains":
                username = (
                    password_entry.username.lower()
                    if not self.is_case_sensitive
                    else password_entry.username
                )
                return value in username

            elif self.rule_type == "email_domain":
                if "@" in password_entry.email:
                    domain = password_entry.email.split("@")[1].lower()
                    return value.lower() in domain

            elif self.rule_type == "custom_field":
                # 检查自定义字段
                for field in password_entry.custom_fields.all():
                    field_value = (
                        field.field_value.lower()
                        if not self.is_case_sensitive
                        else field.field_value
                    )
                    if value in field_value:
                        return True

            return False

        except Exception:
            return False

    def apply_to_entry(self, password_entry):
        """将规则应用到密码条目"""
        if self.test_match(password_entry):
            from django.utils import timezone

            password_entry.category = self.target_category
            password_entry.save(update_fields=["category"])

            # 更新统计信息
            self.match_count += 1
            self.last_matched = timezone.now()
            self.save(update_fields=["match_count", "last_matched"])

            return True
        return False


class CategoryStatistics(models.Model):
    """分类统计模型"""

    category = models.OneToOneField(
        "passwords.Category",
        on_delete=models.CASCADE,
        related_name="statistics",
        verbose_name=_("分类"),
    )

    # 统计数据
    total_passwords = models.IntegerField(default=0, verbose_name=_("密码总数"))
    weak_passwords = models.IntegerField(default=0, verbose_name=_("弱密码数"))
    medium_passwords = models.IntegerField(default=0, verbose_name=_("中等密码数"))
    strong_passwords = models.IntegerField(default=0, verbose_name=_("强密码数"))
    very_strong_passwords = models.IntegerField(
        default=0, verbose_name=_("非常强密码数")
    )

    # 安全统计
    expired_passwords = models.IntegerField(default=0, verbose_name=_("过期密码数"))
    duplicate_passwords = models.IntegerField(default=0, verbose_name=_("重复密码数"))

    # 使用统计
    total_accesses = models.IntegerField(default=0, verbose_name=_("总访问次数"))
    last_accessed = models.DateTimeField(
        null=True, blank=True, verbose_name=_("最后访问时间")
    )

    # 时间戳
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("分类统计")
        verbose_name_plural = _("分类统计")
        db_table = "category_statistics"

    def __str__(self):
        return f"{self.category.name} - 统计"

    def refresh_statistics(self):
        """刷新统计数据"""
        from django.utils import timezone

        passwords = self.category.passwordentry_set.all()

        # 基本统计
        self.total_passwords = passwords.count()

        # 密码强度统计
        self.weak_passwords = passwords.filter(strength="weak").count()
        self.medium_passwords = passwords.filter(strength="medium").count()
        self.strong_passwords = passwords.filter(strength="strong").count()
        self.very_strong_passwords = passwords.filter(strength="very_strong").count()

        # 安全统计
        self.expired_passwords = passwords.filter(expires_at__lt=timezone.now()).count()

        # 重复密码检查（这里简化处理）
        # 实际应用中需要解密密码进行比较
        self.duplicate_passwords = 0

        # 访问统计
        access_logs = []
        for password in passwords:
            access_logs.extend(password.access_logs.all())

        self.total_accesses = len(access_logs)
        if access_logs:
            self.last_accessed = max(log.created_at for log in access_logs)

        self.save()
