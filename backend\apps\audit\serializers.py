from rest_framework import serializers
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema_field
from .models import OperationLog, PasswordAccessLog, SecurityEvent

User = get_user_model()


class OperationLogSerializer(serializers.ModelSerializer):
    """
    操作日志序列化器

    用于序列化系统操作日志，包含用户操作的详细信息。

    字段说明：
    - id: 日志唯一标识
    - user: 操作用户ID
    - user_name: 操作用户全名
    - user_email: 操作用户邮箱
    - action_type: 操作类型（如：create, update, delete等）
    - action_display: 操作类型显示名称
    - result: 操作结果（success/failure）
    - target_type: 目标对象类型
    - target_id: 目标对象ID
    - target_name: 目标对象名称
    - description: 操作描述
    - extra_data: 额外数据（JSON格式）
    - ip_address: 操作者IP地址
    - user_agent: 用户代理字符串
    - request_method: HTTP请求方法
    - request_path: 请求路径
    - created_at: 创建时间（ISO 8601格式）
    """

    user_name = serializers.CharField(
        source="user.get_full_name", read_only=True, help_text="操作用户的全名"
    )
    user_email = serializers.CharField(
        source="user.email", read_only=True, help_text="操作用户的邮箱地址"
    )
    action_display = serializers.CharField(
        source="get_action_type_display", read_only=True, help_text="操作类型的显示名称"
    )

    class Meta:
        model = OperationLog
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "action_type",
            "action_display",
            "result",
            "target_type",
            "target_id",
            "target_name",
            "description",
            "extra_data",
            "ip_address",
            "user_agent",
            "request_method",
            "request_path",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class PasswordAccessLogSerializer(serializers.ModelSerializer):
    """密码访问日志序列化器"""

    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    user_email = serializers.CharField(source="user.email", read_only=True)
    access_type_display = serializers.CharField(
        source="get_access_type_display", read_only=True
    )
    password_title = serializers.CharField(
        source="password_entry.title", read_only=True
    )

    class Meta:
        model = PasswordAccessLog
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "password_entry",
            "password_title",
            "access_type",
            "access_type_display",
            "ip_address",
            "user_agent",
            "access_source",
            "source_id",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class SecurityEventSerializer(serializers.ModelSerializer):
    """安全事件序列化器"""

    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    user_email = serializers.CharField(source="user.email", read_only=True)
    event_type_display = serializers.CharField(
        source="get_event_type_display", read_only=True
    )
    severity_display = serializers.CharField(
        source="get_severity_display", read_only=True
    )

    class Meta:
        model = SecurityEvent
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "event_type",
            "event_type_display",
            "severity",
            "severity_display",
            "status",
            "title",
            "description",
            "event_data",
            "affected_resources",
            "assigned_to",
            "resolution_notes",
            "resolved_at",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class AuditStatsSerializer(serializers.Serializer):
    """审计统计序列化器"""

    total_operations = serializers.IntegerField(read_only=True)
    total_accesses = serializers.IntegerField(read_only=True)
    total_security_events = serializers.IntegerField(read_only=True)
    unresolved_security_events = serializers.IntegerField(read_only=True)

    # 按时间统计
    operations_by_hour = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )
    operations_by_day = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )

    # 按用户统计
    top_active_users = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )

    # 按操作类型统计
    operations_by_action = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )

    # 按资源类型统计
    operations_by_resource = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )

    # 安全事件统计
    security_events_by_type = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )
    security_events_by_severity = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )


class UserActivitySerializer(serializers.Serializer):
    """用户活动序列化器"""

    user_id = serializers.IntegerField()
    user_name = serializers.CharField()
    user_email = serializers.CharField()

    # 操作统计
    total_operations = serializers.IntegerField()
    last_operation_time = serializers.DateTimeField()

    # 访问统计
    total_accesses = serializers.IntegerField()
    last_access_time = serializers.DateTimeField()

    # 安全事件统计
    security_events_count = serializers.IntegerField()
    last_security_event_time = serializers.DateTimeField(allow_null=True)

    # 最近活动
    recent_operations = OperationLogSerializer(many=True, read_only=True)
    recent_accesses = PasswordAccessLogSerializer(many=True, read_only=True)


class AuditSearchSerializer(serializers.Serializer):
    """审计搜索序列化器"""

    log_type = serializers.ChoiceField(
        choices=[
            ("operation", "操作日志"),
            ("access", "访问日志"),
            ("security", "安全事件"),
        ],
        required=False,
        help_text="日志类型",
    )

    user_id = serializers.IntegerField(required=False, help_text="用户ID")

    action = serializers.CharField(required=False, max_length=50, help_text="操作类型")

    resource_type = serializers.CharField(
        required=False, max_length=50, help_text="资源类型"
    )

    resource_id = serializers.IntegerField(required=False, help_text="资源ID")

    start_time = serializers.DateTimeField(required=False, help_text="开始时间")

    end_time = serializers.DateTimeField(required=False, help_text="结束时间")

    ip_address = serializers.IPAddressField(required=False, help_text="IP地址")

    keyword = serializers.CharField(
        required=False, max_length=100, help_text="关键词搜索"
    )

    def validate(self, attrs):
        """验证搜索参数"""
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")

        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError("开始时间必须早于结束时间")

        return attrs


class SecurityEventCreateSerializer(serializers.ModelSerializer):
    """安全事件创建序列化器"""

    class Meta:
        model = SecurityEvent
        fields = [
            "user",
            "event_type",
            "severity",
            "title",
            "description",
            "event_data",
            "affected_resources",
        ]

    def validate_severity(self, value):
        """验证严重程度"""
        if value not in ["low", "medium", "high", "critical"]:
            raise serializers.ValidationError("无效的严重程度")
        return value


class SecurityEventResolveSerializer(serializers.Serializer):
    """安全事件解决序列化器"""

    resolution_notes = serializers.CharField(required=False, help_text="解决说明")

    def update(self, instance, validated_data):
        """标记安全事件为已解决"""
        instance.status = "resolved"
        instance.resolved_at = timezone.now()
        instance.assigned_to = self.context["request"].user

        # 添加解决说明
        if validated_data.get("resolution_notes"):
            instance.resolution_notes = validated_data["resolution_notes"]

        instance.save()
        return instance


class AuditExportSerializer(serializers.Serializer):
    """审计导出序列化器"""

    log_type = serializers.ChoiceField(
        choices=[
            ("operation", "操作日志"),
            ("access", "访问日志"),
            ("security", "安全事件"),
        ],
        help_text="日志类型",
    )

    format = serializers.ChoiceField(
        choices=[("csv", "CSV"), ("excel", "Excel"), ("json", "JSON")],
        default="csv",
        help_text="导出格式",
    )

    start_time = serializers.DateTimeField(required=False, help_text="开始时间")

    end_time = serializers.DateTimeField(required=False, help_text="结束时间")

    user_ids = serializers.ListField(
        child=serializers.IntegerField(), required=False, help_text="用户ID列表"
    )

    include_details = serializers.BooleanField(
        default=True, help_text="是否包含详细信息"
    )

    def validate(self, attrs):
        """验证导出参数"""
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")

        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError("开始时间必须早于结束时间")

        return attrs
