#!/usr/bin/env python
"""
API测试脚本
用于测试密码管理系统的所有API端点
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


class APITester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.client = APIClient()
        self.token = None
        self.user = None

    def setup_test_user(self):
        """创建测试用户"""
        try:
            self.user = User.objects.get(username="testuser")
        except User.DoesNotExist:
            self.user = User.objects.create_user(
                username="testuser",
                email="<EMAIL>",
                password="testpass123",
                first_name="Test",
                last_name="User",
            )

        # 获取JWT token
        refresh = RefreshToken.for_user(self.user)
        self.token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")

        print(f"✓ 测试用户创建成功: {self.user.username}")
        print(f"✓ JWT Token: {self.token[:50]}...")

    def test_endpoint(self, method, url, data=None, expected_status=200):
        """测试API端点"""
        try:
            if method.upper() == "GET":
                response = self.client.get(url)
            elif method.upper() == "POST":
                response = self.client.post(url, data, format="json")
            elif method.upper() == "PUT":
                response = self.client.put(url, data, format="json")
            elif method.upper() == "PATCH":
                response = self.client.patch(url, data, format="json")
            elif method.upper() == "DELETE":
                response = self.client.delete(url)
            else:
                return False, f"不支持的HTTP方法: {method}"

            success = response.status_code == expected_status
            return success, {
                "status_code": response.status_code,
                "data": (
                    response.data
                    if hasattr(response, "data")
                    else response.content.decode()
                ),
            }
        except Exception as e:
            return False, str(e)

    def run_tests(self):
        """运行所有API测试"""
        print("\n" + "=" * 50)
        print("开始API测试")
        print("=" * 50)

        # 设置测试用户
        self.setup_test_user()

        # 测试用例
        test_cases = [
            # API文档
            ("GET", "/api/docs/", None, 200, "API文档"),
            # 认证相关
            ("GET", "/api/auth/profile/", None, 200, "用户资料"),
            ("GET", "/api/auth/users/", None, 200, "用户列表"),
            ("GET", "/api/auth/departments/", None, 200, "部门列表"),
            ("GET", "/api/auth/teams/", None, 200, "团队列表"),
            ("GET", "/api/auth/roles/", None, 200, "角色列表"),
            # 密码管理
            ("GET", "/api/passwords/entries/", None, 200, "密码条目列表"),
            ("GET", "/api/passwords/categories/", None, 200, "分类列表"),
            ("GET", "/api/passwords/tags/", None, 200, "标签列表"),
            (
                "POST",
                "/api/passwords/generator/",
                {"length": 12, "include_uppercase": True},
                200,
                "密码生成器",
            ),
            ("GET", "/api/passwords/security-analysis/", None, 200, "安全分析"),
            # 密码分享
            ("GET", "/api/sharing/share-links/", None, 200, "分享链接列表"),
            # 审计日志
            ("GET", "/api/audit/operation-logs/", None, 200, "操作日志"),
            ("GET", "/api/audit/access-logs/", None, 200, "访问日志"),
            ("GET", "/api/audit/security-events/", None, 200, "安全事件"),
            ("GET", "/api/audit/stats/", None, 200, "审计统计"),
            ("GET", "/api/audit/user-activity/", None, 200, "用户活动"),
            # 系统管理
            ("GET", "/api/system/settings/", None, 200, "系统设置"),
            ("GET", "/api/system/email-templates/", None, 200, "邮件模板"),
            ("GET", "/api/system/backup-configs/", None, 200, "备份配置"),
            ("GET", "/api/system/status/", None, 200, "系统状态"),
        ]

        results = []
        for method, url, data, expected_status, description in test_cases:
            print(f"\n测试: {description} ({method} {url})")
            success, result = self.test_endpoint(method, url, data, expected_status)

            if success:
                print(f"✓ 成功 - 状态码: {result['status_code']}")
            else:
                print(f"✗ 失败 - {result}")

            results.append(
                {
                    "description": description,
                    "method": method,
                    "url": url,
                    "success": success,
                    "result": result,
                }
            )

        # 统计结果
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r["success"])
        failed_tests = total_tests - passed_tests

        print("\n" + "=" * 50)
        print("测试结果统计")
        print("=" * 50)
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")

        if failed_tests > 0:
            print("\n失败的测试:")
            for result in results:
                if not result["success"]:
                    print(f"- {result['description']}: {result['result']}")

        return results


def main():
    """主函数"""
    tester = APITester()
    results = tester.run_tests()

    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"api_test_report_{timestamp}.json"

    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)

    print(f"\n测试报告已保存到: {report_file}")


if __name__ == "__main__":
    main()
