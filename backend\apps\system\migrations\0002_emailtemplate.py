# Generated by Django 5.2.4 on 2025-07-28 14:45

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='模板名称')),
                ('template_type', models.CharField(choices=[('welcome', '欢迎邮件'), ('password_reset', '密码重置'), ('account_locked', '账户锁定'), ('security_alert', '安全警报'), ('backup_notification', '备份通知'), ('system_maintenance', '系统维护'), ('custom', '自定义')], max_length=20, verbose_name='模板类型')),
                ('subject', models.CharField(max_length=200, verbose_name='邮件主题')),
                ('content', models.TextField(verbose_name='邮件内容')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('is_default', models.BooleanField(default=False, verbose_name='默认模板')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '邮件模板',
                'verbose_name_plural': '邮件模板',
                'db_table': 'email_templates',
                'ordering': ['template_type', 'name'],
                'unique_together': {('template_type', 'is_default')},
            },
        ),
    ]
