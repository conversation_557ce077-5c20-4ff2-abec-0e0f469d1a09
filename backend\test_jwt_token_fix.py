#!/usr/bin/env python
"""
测试JWT令牌修复的脚本
"""
import requests
import json
import time


def test_jwt_token_fix():
    """测试JWT令牌修复"""
    base_url = "http://localhost:8001/api"

    print("=== 测试JWT令牌修复 ===")

    # 1. 测试登录获取令牌
    print("\n1. 测试登录获取令牌")
    login_data = {"username": "admin", "password": "admin123"}

    try:
        login_response = requests.post(f"{base_url}/auth/login/", json=login_data)
        print(f"登录响应状态: {login_response.status_code}")

        if login_response.status_code == 200:
            login_result = login_response.json()
            print(f"✅ 登录成功")

            # 检查返回的令牌
            access_token = login_result.get("access_token")
            refresh_token = login_result.get("refresh_token")

            if access_token:
                print(f"   - Access Token: {access_token[:50]}...")
            else:
                print(f"   ❌ 缺少 Access Token")
                return

            if refresh_token:
                print(f"   - Refresh Token: {refresh_token[:50]}...")
            else:
                print(f"   ❌ 缺少 Refresh Token")
                return

            headers = {"Authorization": f"Bearer {access_token}"}

            # 2. 测试使用Access Token访问API
            print(f"\n2. 测试使用Access Token访问API")
            try:
                response = requests.get(f"{base_url}/auth/profile/", headers=headers)
                if response.status_code == 200:
                    user_info = response.json()
                    print(
                        f"✅ Access Token有效，用户: {user_info.get('username', '未知')}"
                    )
                else:
                    print(f"❌ Access Token无效: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"❌ API请求失败: {e}")

            # 3. 测试刷新令牌
            print(f"\n3. 测试刷新令牌")
            try:
                refresh_data = {"refresh": refresh_token}
                response = requests.post(
                    f"{base_url}/auth/token/refresh/", json=refresh_data
                )
                print(f"刷新令牌响应状态: {response.status_code}")

                if response.status_code == 200:
                    refresh_result = response.json()
                    new_access_token = refresh_result.get("access")
                    new_refresh_token = refresh_result.get("refresh")

                    if new_access_token:
                        print(
                            f"✅ 刷新成功，新Access Token: {new_access_token[:50]}..."
                        )

                        # 测试新令牌是否有效
                        new_headers = {"Authorization": f"Bearer {new_access_token}"}
                        response = requests.get(
                            f"{base_url}/auth/profile/", headers=new_headers
                        )
                        if response.status_code == 200:
                            print(f"✅ 新Access Token有效")
                        else:
                            print(f"❌ 新Access Token无效: {response.status_code}")
                    else:
                        print(f"❌ 刷新响应中缺少新的Access Token")

                    if new_refresh_token:
                        print(f"✅ 获得新Refresh Token: {new_refresh_token[:50]}...")
                    else:
                        print(f"⚠️ 没有新的Refresh Token（可能使用旧的）")
                else:
                    print(f"❌ 刷新令牌失败: {response.status_code}")
                    print(f"   错误详情: {response.text}")
            except requests.exceptions.RequestException as e:
                print(f"❌ 刷新令牌请求失败: {e}")

            # 4. 测试令牌过期处理（模拟）
            print(f"\n4. 测试无效令牌处理")
            invalid_token = "invalid.token.here"
            invalid_headers = {"Authorization": f"Bearer {invalid_token}"}

            try:
                response = requests.get(
                    f"{base_url}/auth/profile/", headers=invalid_headers
                )
                if response.status_code == 401:
                    print(f"✅ 无效令牌正确返回401状态")
                else:
                    print(f"⚠️ 无效令牌返回状态: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"❌ 无效令牌测试失败: {e}")

            # 5. 测试登出功能
            print(f"\n5. 测试登出功能")
            try:
                logout_data = {"refresh_token": refresh_token}
                response = requests.post(
                    f"{base_url}/auth/logout/", json=logout_data, headers=headers
                )
                if response.status_code == 200:
                    print(f"✅ 登出成功")

                    # 测试登出后令牌是否失效
                    response = requests.get(
                        f"{base_url}/auth/profile/", headers=headers
                    )
                    if response.status_code == 401:
                        print(f"✅ 登出后令牌已失效")
                    else:
                        print(f"⚠️ 登出后令牌状态: {response.status_code}")
                else:
                    print(f"❌ 登出失败: {response.status_code}")
                    print(f"   错误详情: {response.text}")
            except requests.exceptions.RequestException as e:
                print(f"❌ 登出测试失败: {e}")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"   错误详情: {login_response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求失败: {e}")

    print("\n=== JWT令牌修复测试完成 ===")
    print("\n📋 修复检查清单:")
    print("2. ✅ 修复前端令牌失效问题")
    print("   - 配置SIMPLE_JWT设置，统一JWT配置")
    print("   - 修复前端刷新令牌API路径")
    print("   - 修复前端刷新令牌参数传递")
    print("   - 添加refresh_token的保存和使用逻辑")
    print("   - 优化令牌自动刷新机制")
    print("   - 改善令牌过期处理逻辑")


if __name__ == "__main__":
    test_jwt_token_fix()
