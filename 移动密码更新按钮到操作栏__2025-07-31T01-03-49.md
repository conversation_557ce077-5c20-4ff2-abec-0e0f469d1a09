请添加以下任务到TaskList
[ ] NAME:Current Task List DESCRIPTION:Root task 
-[x] NAME:Task 14: Password Authorization UX Design Analysis DESCRIPTION:Analyze and document the password authorization implementation process from a usability and user experience perspective. IMPORTANT: Do not implement any code until user approval is received for the proposed design.
--[x] NAME:UX Research: Analyze Current Authorization Flow DESCRIPTION:Document the current password authorization implementation and identify UX pain points
--[x] NAME:UX Design: Create User Flow Diagrams DESCRIPTION:Design comprehensive user flows for password authorization process
--[x] NAME:UX Design: Create Wireframes and Mockups DESCRIPTION:Create detailed wireframes and UI mockups for all authorization-related pages and components
--[x] NAME:UX Specification: Document Component Requirements DESCRIPTION:Specify exact UI components, interactions, and technical requirements
--[x] NAME:UX Review: Present Design for User Approval DESCRIPTION:Present the complete UX specification to user for approval before implementation
-[x] NAME:Task 15: Reorganize Password Groups Menu DESCRIPTION:Move 'Password Groups' (密码组) from System Management menu to Password Management menu
--[x] NAME:Frontend: Update Navigation Menu Structure DESCRIPTION:Move Password Groups menu item from System Management to Password Management section
--[x] NAME:Frontend: Update Menu Routing DESCRIPTION:Ensure proper routing and navigation for moved Password Groups menu item
--[x] NAME:Frontend: Verify Access Permissions DESCRIPTION:Ensure access permissions remain correct after menu reorganization
-[x] NAME:Task 16: Remove Password Generator Page DESCRIPTION:Remove the standalone password generator page from frontend
--[x] NAME:Frontend: Remove Password Generator Page DESCRIPTION:Remove the standalone password generator page file from frontend
--[x] NAME:Frontend: Clean Up Password Generator Routes DESCRIPTION:Remove password generator routes from router configuration
--[x] NAME:Frontend: Remove Navigation Links DESCRIPTION:Remove password generator navigation links from menus
--[x] NAME:Frontend: Verify Form Generation Functionality DESCRIPTION:Ensure password generation functionality remains available in password creation/edit forms
-[x] NAME:Task 17: Fix Pagination Page Size Selector DESCRIPTION:Fix the pagination page size selector on password list page that is not working
--[x] NAME:Frontend: Debug Pagination Component DESCRIPTION:Investigate why page size selector is not working in password list pagination
--[x] NAME:Frontend: Fix Page Size Change Handler DESCRIPTION:Fix the handlePageSizeChange function to properly update page size and reload data
--[x] NAME:Frontend: Test Page Size Functionality DESCRIPTION:Test page size changes with different values (10, 20, 50, 100 items per page)
--[x] NAME:Frontend: Verify Immediate Effect DESCRIPTION:Ensure page size changes take effect immediately without requiring page refresh
-[x] NAME:Task 18: Improve Favorite Button Visual States DESCRIPTION:Update favorite button icons in password list operations column with proper visual states
--[x] NAME:Frontend: Update Favorite Button Icons DESCRIPTION:Update favorite button to show yellow filled star for favorited passwords and gray outline star for non-favorited
--[x] NAME:Frontend: Implement Proper Hover States DESCRIPTION:Add proper hover states and visual feedback for favorite buttons
--[x] NAME:Frontend: Test Visual States DESCRIPTION:Test favorite button visual states in both table and grid views
-[x] NAME:Task 19: Update Batch Delete Button Text DESCRIPTION:Change batch delete button text from '批量删除' to '批量移动到回收站'
--[x] NAME:Frontend: Update Batch Delete Button Text DESCRIPTION:Change batch delete button text from '批量删除' to '批量移动到回收站'
--[x] NAME:Frontend: Update Confirmation Dialog DESCRIPTION:Update batch delete confirmation dialog messaging to reflect recycle bin movement
--[x] NAME:Frontend: Ensure Consistency DESCRIPTION:Ensure consistency with individual delete button changes already implemented
-[x] NAME:Task 20: Implement Batch Move to Recycle Bin Functionality DESCRIPTION:Implement the actual backend functionality for batch moving passwords to recycle bin
--[x] NAME:Backend: Create Batch Delete API Endpoint DESCRIPTION:Implement backend API endpoint for batch moving passwords to recycle bin
--[x] NAME:Backend: Add Proper Error Handling DESCRIPTION:Add comprehensive error handling for batch delete operations
--[x] NAME:Frontend: Integrate Batch Delete API DESCRIPTION:Connect frontend batch delete functionality with backend API
--[ ] NAME:Frontend: Test Multiple Selections DESCRIPTION:Test batch delete functionality with multiple password selections
-[/] NAME:Task 21: Add Password Sharing Feature DESCRIPTION:Add '分享' (Share) button to both password list page and password detail page with comprehensive sharing modal
--[x] NAME:Backend: Design Sharing Data Model DESCRIPTION:Design database models for password sharing functionality
--[x] NAME:Backend: Create Sharing API Endpoints DESCRIPTION:Implement API endpoints for password sharing operations
--[x] NAME:Backend: Implement One-time Link Generation DESCRIPTION:Implement secure one-time link generation with auto-expiry
--[x] NAME:Backend: Add Sharing Audit Logging DESCRIPTION:Add comprehensive audit logging for all sharing activities
--[/] NAME:Frontend: Add Share Button to Password List DESCRIPTION:Add '分享' (Share) button to password list page operations
--[ ] NAME:Frontend: Add Share Button to Password Detail DESCRIPTION:Add '分享' (Share) button to password detail page
--[ ] NAME:Frontend: Create Sharing Modal Component DESCRIPTION:Create comprehensive sharing modal with all required components (sharing type, user selection, link generation, etc.)
--[ ] NAME:Frontend: Implement User Search Component DESCRIPTION:Create searchable user dropdown/autocomplete component for user selection
--[ ] NAME:Frontend: Add Validation and Error Handling DESCRIPTION:Implement proper validation and error handling for sharing functionality
--[ ] NAME:Frontend: Ensure Mobile Responsiveness DESCRIPTION:Ensure all sharing UI components are mobile responsive
-[ ] NAME:Task 22: Create Password Sharing Management Page DESCRIPTION:Create new page for managing shared passwords with two-tab layout for incoming and outgoing shares
--[ ] NAME:Backend: Create Sharing Management API DESCRIPTION:Create API endpoints for retrieving shared passwords (incoming and outgoing)
--[ ] NAME:Backend: Implement Sharing Filters and Search DESCRIPTION:Implement backend filtering, sorting, and search functionality for shared passwords
--[ ] NAME:Frontend: Create Password Sharing Page DESCRIPTION:Create new '密码分享' (Password Sharing) page accessible from main navigation
--[ ] NAME:Frontend: Implement Two-Tab Layout DESCRIPTION:Create two-tab layout: '分享给我的' (Shared with Me) and '我分享的' (My Shares)
--[ ] NAME:Frontend: Create Shared Passwords Table DESCRIPTION:Implement table with columns: Password Title, Share Target/Source, Share Date, Expiry Date, Shared By/Viewer, Permissions, Actions
--[ ] NAME:Frontend: Add Filtering and Search DESCRIPTION:Add proper filtering, sorting, and search functionality to both tabs
--[ ] NAME:Frontend: Implement View and Revoke Actions DESCRIPTION:Implement View and Delete/Revoke action buttons for shared passwords
--[ ] NAME:Frontend: Add Navigation Menu Item DESCRIPTION:Add '密码分享' menu item to main navigation
--[ ] NAME:Frontend: Ensure Mobile Responsiveness DESCRIPTION:Ensure password sharing management page is mobile responsive

1. 分享密码模态框中生成的链接地址http://example.com，请将该参数替换为当前浏览器的访问地址，并在后续可通过配置文件进行修改
2. 选择分享给用户后，选择用户下拉框没有数据

首先请先清理已完成的任务，然后再将以下任务添加到TaskList中

1. 密码分享页面我分享的和分享给我的数据加载失败，应该时后端服务器错误       <p>
        
            <header id="summary">
    <h1>Page not found <small>(404)</small></h1>
    
    <table class="meta">
      <tr>
2 分享密码模态框中生成的链接地址访问后提示http://localhost:5666/share/xkvfmdj33
3 分享密码模态框中用户下拉菜单还是没有数据
4 分享密码模态框中把备注栏去掉