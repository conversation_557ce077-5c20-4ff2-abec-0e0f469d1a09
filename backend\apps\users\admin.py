from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, Department, Team, Role


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'created_at']
    list_filter = ['created_at', 'parent']
    search_fields = ['name', 'description']
    ordering = ['name']


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    list_display = ['name', 'department', 'created_at']
    list_filter = ['department', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at']
    list_filter = ['name', 'created_at']
    search_fields = ['description']
    ordering = ['name']


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ['username', 'email', 'first_name', 'last_name', 'department', 'team', 'role', 'is_active', 'date_joined']
    list_filter = ['is_active', 'is_staff', 'is_superuser', 'department', 'team', 'role', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering = ['-date_joined']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        (_('扩展信息'), {
            'fields': ('phone', 'avatar', 'department', 'team', 'role')
        }),
        (_('安全设置'), {
            'fields': ('is_mfa_enabled', 'last_password_change', 'failed_login_attempts', 'locked_until')
        }),
    )
    
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        (_('扩展信息'), {
            'fields': ('email', 'phone', 'department', 'team', 'role')
        }),
    )