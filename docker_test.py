#!/usr/bin/env python
"""
Docker部署测试脚本
用于构建Docker镜像、启动服务并运行API测试
"""

import os
import sys
import time
import subprocess
import requests
import json
from datetime import datetime

class DockerTester:
    def __init__(self):
        self.base_url = 'http://localhost:8000'
        self.compose_file = 'docker-compose.yml'
        
    def run_command(self, command, cwd=None):
        """运行命令并返回结果"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=cwd,
                capture_output=True, 
                text=True, 
                timeout=300
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)
    
    def check_docker(self):
        """检查Docker是否可用"""
        print("检查Docker环境...")
        success, stdout, stderr = self.run_command("docker --version")
        if not success:
            print(f"✗ Docker未安装或不可用: {stderr}")
            return False
        print(f"✓ Docker版本: {stdout.strip()}")
        
        success, stdout, stderr = self.run_command("docker-compose --version")
        if not success:
            print(f"✗ Docker Compose未安装或不可用: {stderr}")
            return False
        print(f"✓ Docker Compose版本: {stdout.strip()}")
        return True
    
    def build_and_start(self):
        """构建并启动Docker服务"""
        print("\n构建并启动Docker服务...")
        
        # 停止现有服务
        print("停止现有服务...")
        self.run_command("docker-compose down")
        
        # 构建并启动服务
        print("构建并启动服务...")
        success, stdout, stderr = self.run_command("docker-compose up --build -d")
        if not success:
            print(f"✗ 启动服务失败: {stderr}")
            return False
        
        print("✓ 服务启动成功")
        return True
    
    def wait_for_service(self, max_wait=120):
        """等待服务启动"""
        print(f"\n等待服务启动 (最多等待{max_wait}秒)...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            try:
                response = requests.get(f"{self.base_url}/api/docs/", timeout=5)
                if response.status_code == 200:
                    print("✓ 服务已启动并可访问")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            print(".", end="", flush=True)
            time.sleep(5)
        
        print(f"\n✗ 服务启动超时 ({max_wait}秒)")
        return False
    
    def run_api_tests(self):
        """运行API测试"""
        print("\n开始API测试...")
        
        # 测试用例
        test_cases = [
            ('GET', '/api/docs/', None, 200, 'API文档'),
            ('POST', '/api/auth/login/', {
                'username': 'admin',
                'password': 'admin123'
            }, 200, '管理员登录'),
        ]
        
        results = []
        token = None
        
        for method, endpoint, data, expected_status, description in test_cases:
            print(f"\n测试: {description} ({method} {endpoint})")
            
            try:
                headers = {}
                if token:
                    headers['Authorization'] = f'Bearer {token}'
                
                url = f"{self.base_url}{endpoint}"
                
                if method == 'GET':
                    response = requests.get(url, headers=headers, timeout=10)
                elif method == 'POST':
                    response = requests.post(url, json=data, headers=headers, timeout=10)
                else:
                    print(f"✗ 不支持的HTTP方法: {method}")
                    continue
                
                success = response.status_code == expected_status
                
                if success:
                    print(f"✓ 成功 - 状态码: {response.status_code}")
                    
                    # 如果是登录接口，提取token
                    if endpoint == '/api/auth/login/' and response.status_code == 200:
                        try:
                            token_data = response.json()
                            token = token_data.get('access_token')
                            print(f"✓ 获取到访问令牌: {token[:20]}...")
                        except:
                            print("⚠ 无法解析登录响应")
                else:
                    print(f"✗ 失败 - 期望状态码: {expected_status}, 实际: {response.status_code}")
                    print(f"响应内容: {response.text[:200]}")
                
                results.append({
                    'description': description,
                    'method': method,
                    'endpoint': endpoint,
                    'expected_status': expected_status,
                    'actual_status': response.status_code,
                    'success': success,
                    'response_time': response.elapsed.total_seconds()
                })
                
            except requests.exceptions.RequestException as e:
                print(f"✗ 请求失败: {e}")
                results.append({
                    'description': description,
                    'method': method,
                    'endpoint': endpoint,
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    def show_logs(self):
        """显示服务日志"""
        print("\n=== 后端服务日志 ===")
        success, stdout, stderr = self.run_command("docker-compose logs backend --tail=20")
        if success:
            print(stdout)
        else:
            print(f"获取日志失败: {stderr}")
    
    def cleanup(self):
        """清理资源"""
        print("\n清理Docker资源...")
        self.run_command("docker-compose down")
        print("✓ 清理完成")
    
    def run_full_test(self):
        """运行完整测试流程"""
        print("Docker部署测试开始")
        print("=" * 50)
        
        try:
            # 检查Docker环境
            if not self.check_docker():
                return False
            
            # 构建并启动服务
            if not self.build_and_start():
                return False
            
            # 等待服务启动
            if not self.wait_for_service():
                self.show_logs()
                return False
            
            # 运行API测试
            results = self.run_api_tests()
            
            # 统计结果
            total_tests = len(results)
            passed_tests = sum(1 for r in results if r.get('success', False))
            failed_tests = total_tests - passed_tests
            
            print("\n" + "=" * 50)
            print("Docker测试结果统计")
            print("=" * 50)
            print(f"总测试数: {total_tests}")
            print(f"通过: {passed_tests}")
            print(f"失败: {failed_tests}")
            print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
            
            if failed_tests > 0:
                print("\n失败的测试:")
                for result in results:
                    if not result.get('success', False):
                        print(f"- {result['description']}: {result.get('error', '状态码不匹配')}")
            
            # 保存测试报告
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f'docker_test_report_{timestamp}.json'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n测试报告已保存到: {report_file}")
            
            return passed_tests == total_tests
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            return False
        except Exception as e:
            print(f"\n测试过程中发生错误: {e}")
            return False
        finally:
            # 显示日志（如果测试失败）
            if failed_tests > 0:
                self.show_logs()

def main():
    """主函数"""
    tester = DockerTester()
    
    try:
        success = tester.run_full_test()
        if success:
            print("\n🎉 所有测试通过！Docker部署成功！")
        else:
            print("\n❌ 部分测试失败，请检查日志")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        tester.cleanup()
        sys.exit(1)
    finally:
        # 询问是否保持服务运行
        try:
            keep_running = input("\n是否保持服务运行？(y/N): ").lower().strip()
            if keep_running != 'y':
                tester.cleanup()
        except KeyboardInterrupt:
            tester.cleanup()

if __name__ == '__main__':
    main()