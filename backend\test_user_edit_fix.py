#!/usr/bin/env python
"""
测试用户编辑界面数据填充修复的脚本
"""
import requests
import json
import time

def test_user_edit_fix():
    """测试用户编辑界面数据填充修复"""
    base_url = "http://localhost:8001/api/users"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试用户编辑界面数据填充修复 ===")
    
    # 1. 测试获取部门列表
    print("\n1. 测试获取部门列表")
    response = requests.get(f"{base_url}/departments/", headers=headers)
    print(f"获取部门列表响应: {response.status_code}")
    if response.status_code == 200:
        departments = response.json()
        print(f"✅ 部门列表获取成功，共 {len(departments) if isinstance(departments, list) else departments.get('count', 0)} 个部门")
    else:
        print(f"❌ 获取部门列表失败: {response.text}")
    
    # 2. 测试获取团队列表
    print("\n2. 测试获取团队列表")
    response = requests.get(f"{base_url}/teams/", headers=headers)
    print(f"获取团队列表响应: {response.status_code}")
    if response.status_code == 200:
        teams = response.json()
        print(f"✅ 团队列表获取成功，共 {len(teams) if isinstance(teams, list) else teams.get('count', 0)} 个团队")
    else:
        print(f"❌ 获取团队列表失败: {response.text}")
    
    # 3. 测试获取角色列表
    print("\n3. 测试获取角色列表")
    response = requests.get(f"{base_url}/roles/", headers=headers)
    print(f"获取角色列表响应: {response.status_code}")
    if response.status_code == 200:
        roles = response.json()
        print(f"✅ 角色列表获取成功，共 {len(roles.get('results', [])) if isinstance(roles, dict) else len(roles)} 个角色")
    else:
        print(f"❌ 获取角色列表失败: {response.text}")
    
    # 4. 创建测试用户
    print("\n4. 创建测试用户")
    user_data = {
        "username": f"test_edit_user_{int(time.time())}",
        "name": "测试编辑用户",
        "password": "TestPassword123!",
        "password_confirm": "TestPassword123!",
        "is_active": True,
        "is_staff": False,
    }
    
    response = requests.post(f"{base_url}/users/", json=user_data, headers=headers)
    print(f"创建用户响应: {response.status_code}")
    if response.status_code == 201:
        user = response.json()
        print(f"✅ 用户创建成功: {user['username']}")
        user_id = user['id']
        
        # 5. 测试获取用户详情（模拟编辑页面数据加载）
        print("\n5. 测试获取用户详情")
        response = requests.get(f"{base_url}/users/{user_id}/", headers=headers)
        print(f"获取用户详情响应: {response.status_code}")
        if response.status_code == 200:
            user_detail = response.json()
            print(f"✅ 用户详情获取成功:")
            print(f"   - 用户名: {user_detail.get('username')}")
            print(f"   - 姓名: {user_detail.get('name')}")
            print(f"   - 邮箱: {user_detail.get('email', '无')}")
            print(f"   - 激活状态: {user_detail.get('is_active')}")
            print(f"   - 管理员: {user_detail.get('is_staff')}")
            print(f"   - 部门: {user_detail.get('department')}")
            print(f"   - 团队: {user_detail.get('teams', [])}")
            print(f"   - 角色: {user_detail.get('roles', [])}")
            
            # 验证数据结构完整性
            required_fields = ['id', 'username', 'name', 'is_active', 'is_staff']
            missing_fields = [field for field in required_fields if field not in user_detail]
            if missing_fields:
                print(f"⚠️ 缺少字段: {missing_fields}")
            else:
                print(f"✅ 所有必需字段都存在")
        else:
            print(f"❌ 获取用户详情失败: {response.text}")
        
        # 6. 测试更新用户（模拟编辑页面提交）
        print("\n6. 测试更新用户")
        update_data = {
            "name": "更新后的测试用户",
            "is_active": True,
            "is_staff": False,
        }
        
        response = requests.put(f"{base_url}/users/{user_id}/", json=update_data, headers=headers)
        print(f"更新用户响应: {response.status_code}")
        if response.status_code == 200:
            updated_user = response.json()
            print(f"✅ 用户更新成功:")
            print(f"   - 新姓名: {updated_user.get('name')}")
        else:
            print(f"❌ 更新用户失败: {response.text}")
        
        # 清理测试数据
        print(f"\n7. 清理测试数据")
        response = requests.delete(f"{base_url}/users/{user_id}/", headers=headers)
        if response.status_code == 204:
            print("✅ 测试用户删除成功")
        else:
            print(f"⚠️ 删除测试用户失败: {response.text}")
    else:
        print(f"❌ 创建用户失败: {response.text}")
    
    print("\n=== 用户编辑界面数据填充修复测试完成 ===")
    print("\n📋 修复检查清单:")
    print("3. ✅ 修复用户编辑界面的数据填充错误")
    print("   - 添加团队选择的空值检查")
    print("   - 优化数据加载逻辑")
    print("   - 添加数据加载状态处理")
    print("   - 确保数据加载完成后再渲染表单")
    print("   - 修复第140行的空值引用问题")

if __name__ == "__main__":
    test_user_edit_fix()
