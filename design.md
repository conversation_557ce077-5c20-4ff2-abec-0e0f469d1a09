# 密码管理系统设计文档

## 1. 系统概述

### 1.1 设计目标
本设计文档基于需求文档，为AI智能体提供详细的技术实现指导，包括数据库设计、API接口设计、前端页面布局和功能设计等。

### 1.2 技术栈
- **前端**: Vue.js 3 + vben-admin + Ant Design Vue + Pinia + TypeScript
- **后端**: Django 4.2 + Django REST Framework + MySQL 8
- **安全**: JWT认证 + SM4加密 + HTTPS
- **部署**: Docker + Nginx + Gunicorn

## 2. 数据库设计

### 2.1 数据表结构

#### 2.1.1 用户表 (users_user)
```sql
CREATE TABLE users_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL COMMENT '邮箱',
    username VARCHAR(150) UNIQUE NOT NULL COMMENT '用户名',
    first_name VARCHA<PERSON>(30) COMMENT '名',
    last_name VARCHA<PERSON>(30) COMMENT '姓',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    is_staff BOOLEAN DEFAULT FALSE COMMENT '是否管理员',
    is_superuser BOOLEAN DEFAULT FALSE COMMENT '是否超级管理员',
    date_joined DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_login DATETIME COMMENT '最后登录时间',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    master_password_set BOOLEAN DEFAULT FALSE COMMENT '是否已设置主密码',
    mfa_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用MFA',
    mfa_secret VARCHAR(32) COMMENT 'MFA密钥',
    failed_login_attempts INT DEFAULT 0 COMMENT '登录失败次数',
    locked_until DATETIME COMMENT '锁定到期时间',
    department_id BIGINT COMMENT '部门ID',
    phone VARCHAR(20) COMMENT '电话',
    avatar VARCHAR(255) COMMENT '头像URL',
    INDEX idx_email (email),
    INDEX idx_department (department_id)
);
```

#### 2.1.2 部门表 (users_department)
```sql
CREATE TABLE users_department (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    description TEXT COMMENT '部门描述',
    parent_id BIGINT COMMENT '父部门ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent (parent_id)
);
```

#### 2.1.3 角色表 (users_role)
```sql
CREATE TABLE users_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    permissions JSON COMMENT '权限列表',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2.1.4 用户角色关联表 (users_user_roles)
```sql
CREATE TABLE users_user_roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    assigned_by BIGINT COMMENT '分配者ID',
    UNIQUE KEY unique_user_role (user_id, role_id),
    INDEX idx_user (user_id),
    INDEX idx_role (role_id)
);
```

#### 2.1.5 密码分类表 (passwords_category)
```sql
CREATE TABLE passwords_category (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    color VARCHAR(7) DEFAULT '#1890ff' COMMENT '分类颜色',
    icon VARCHAR(50) COMMENT '图标',
    parent_id BIGINT COMMENT '父分类ID',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent (parent_id),
    INDEX idx_creator (created_by)
);
```

#### 2.1.6 密码标签表 (passwords_tag)
```sql
CREATE TABLE passwords_tag (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '标签名称',
    color VARCHAR(7) DEFAULT '#87d068' COMMENT '标签颜色',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_creator (created_by)
);
```

#### 2.1.7 密码条目表 (passwords_passwordentry)
```sql
CREATE TABLE passwords_passwordentry (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '标题',
    username VARCHAR(255) COMMENT '用户名',
    password_encrypted TEXT NOT NULL COMMENT '加密后的密码',
    url VARCHAR(500) COMMENT '网址',
    host VARCHAR(255) COMMENT '主机地址',
    port INT COMMENT '端口',
    database_name VARCHAR(100) COMMENT '数据库名',
    connection_type VARCHAR(50) COMMENT '连接类型',
    account_type VARCHAR(50) NOT NULL COMMENT '账号类型',
    notes TEXT COMMENT '备注',
    custom_fields JSON COMMENT '自定义字段',
    category_id BIGINT COMMENT '分类ID',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_accessed DATETIME COMMENT '最后访问时间',
    expires_at DATETIME COMMENT '过期时间',
    is_favorite BOOLEAN DEFAULT FALSE COMMENT '是否收藏',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    deleted_at DATETIME COMMENT '删除时间',
    password_strength INT COMMENT '密码强度评分',
    INDEX idx_title (title),
    INDEX idx_category (category_id),
    INDEX idx_creator (created_by),
    INDEX idx_account_type (account_type),
    INDEX idx_deleted (is_deleted),
    FULLTEXT idx_search (title, username, notes)
);
```

#### 2.1.8 密码条目标签关联表 (passwords_passwordentry_tags)
```sql
CREATE TABLE passwords_passwordentry_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    passwordentry_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    UNIQUE KEY unique_entry_tag (passwordentry_id, tag_id),
    INDEX idx_entry (passwordentry_id),
    INDEX idx_tag (tag_id)
);
```

#### 2.1.9 密码分享记录表 (sharing_sharerecord)
```sql
CREATE TABLE sharing_sharerecord (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    password_entry_id BIGINT NOT NULL COMMENT '密码条目ID',
    shared_by BIGINT NOT NULL COMMENT '分享者ID',
    shared_to BIGINT COMMENT '接收者ID',
    share_token VARCHAR(64) UNIQUE NOT NULL COMMENT '分享令牌',
    share_type VARCHAR(20) NOT NULL COMMENT '分享类型',
    permission VARCHAR(20) DEFAULT 'read' COMMENT '权限类型',
    expires_at DATETIME COMMENT '过期时间',
    max_views INT DEFAULT 1 COMMENT '最大查看次数',
    view_count INT DEFAULT 0 COMMENT '已查看次数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_accessed DATETIME COMMENT '最后访问时间',
    INDEX idx_token (share_token),
    INDEX idx_entry (password_entry_id),
    INDEX idx_shared_by (shared_by),
    INDEX idx_shared_to (shared_to)
);
```

#### 2.1.10 操作日志表 (audit_operationlog)
```sql
CREATE TABLE audit_operationlog (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT COMMENT '操作用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id BIGINT COMMENT '资源ID',
    details JSON COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    success BOOLEAN DEFAULT TRUE COMMENT '是否成功',
    error_message TEXT COMMENT '错误信息',
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_timestamp (timestamp),
    INDEX idx_resource (resource_type, resource_id)
);
```

#### 2.1.11 系统设置表 (system_setting)
```sql
CREATE TABLE system_setting (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(100) UNIQUE NOT NULL COMMENT '设置键',
    value TEXT COMMENT '设置值',
    description TEXT COMMENT '设置描述',
    category VARCHAR(50) COMMENT '设置分类',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (key_name),
    INDEX idx_category (category)
);
```

### 2.2 数据库索引优化

#### 2.2.1 复合索引
```sql
-- 密码条目查询优化
CREATE INDEX idx_password_search ON passwords_passwordentry (created_by, is_deleted, category_id);
CREATE INDEX idx_password_type_time ON passwords_passwordentry (account_type, created_at);

-- 分享记录查询优化
CREATE INDEX idx_share_active_expires ON sharing_sharerecord (is_active, expires_at);

-- 操作日志查询优化
CREATE INDEX idx_log_user_time ON audit_operationlog (user_id, timestamp);
```

## 3. 后端API设计

### 3.1 API基础规范

#### 3.1.1 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 3.1.2 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "errors": {
    "field_name": ["错误信息"]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 3.1.3 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "results": [],
    "count": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 3.2 认证与授权API

#### 3.2.1 用户登录
```
POST /api/auth/login
Content-Type: application/json

请求体:
{
  "email": "<EMAIL>",
  "password": "password123",
  "mfa_code": "123456",
  "remember_me": true
}

响应:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "user",
      "first_name": "张",
      "last_name": "三",
      "avatar": "/media/avatars/user.jpg",
      "department": {
        "id": 1,
        "name": "技术部"
      },
      "roles": ["user"],
      "permissions": ["view_password", "create_password"],
      "master_password_set": true,
      "mfa_enabled": false
    }
  }
}
```

#### 3.2.2 刷新Token
```
POST /api/auth/refresh
Content-Type: application/json

请求体:
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}

响应:
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  }
}
```

#### 3.2.3 设置主密码
```
POST /api/auth/master-password
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "master_password": "masterpass123",
  "confirm_password": "masterpass123"
}

响应:
{
  "code": 200,
  "message": "主密码设置成功",
  "data": {
    "master_password_set": true
  }
}
```

### 3.3 用户管理API

#### 3.3.1 获取用户信息
```
GET /api/users/profile
Authorization: Bearer {access_token}

响应:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user",
    "first_name": "张",
    "last_name": "三",
    "phone": "***********",
    "avatar": "/media/avatars/user.jpg",
    "department": {
      "id": 1,
      "name": "技术部"
    },
    "roles": ["user"],
    "mfa_enabled": false,
    "last_login": "2024-01-01T10:00:00Z",
    "date_joined": "2024-01-01T00:00:00Z"
  }
}
```

#### 3.3.2 更新用户信息
```
PUT /api/users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "first_name": "张",
  "last_name": "三",
  "phone": "***********"
}

响应:
{
  "code": 200,
  "message": "用户信息更新成功",
  "data": {
    "id": 1,
    "first_name": "张",
    "last_name": "三",
    "phone": "***********"
  }
}
```

### 3.4 密码管理API

#### 3.4.1 获取密码列表
```
GET /api/passwords?page=1&page_size=20&category=1&search=test&account_type=database
Authorization: Bearer {access_token}

响应:
{
  "code": 200,
  "message": "success",
  "data": {
    "results": [
      {
        "id": 1,
        "title": "生产数据库",
        "username": "admin",
        "url": "mysql://prod.example.com",
        "host": "prod.example.com",
        "port": 3306,
        "account_type": "database",
        "category": {
          "id": 1,
          "name": "数据库",
          "color": "#1890ff"
        },
        "tags": [
          {
            "id": 1,
            "name": "生产环境",
            "color": "#f50"
          }
        ],
        "password_strength": 85,
        "is_favorite": false,
        "expires_at": "2024-12-31T23:59:59Z",
        "last_accessed": "2024-01-01T10:00:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "count": 1,
    "page": 1,
    "page_size": 20,
    "total_pages": 1
  }
}
```

#### 3.4.2 创建密码条目
```
POST /api/passwords
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "title": "测试服务器",
  "username": "root",
  "password": "password123",
  "host": "test.example.com",
  "port": 22,
  "account_type": "server",
  "category_id": 1,
  "tag_ids": [1, 2],
  "notes": "测试环境服务器",
  "custom_fields": {
    "责任人": "张三",
    "环境": "测试"
  },
  "expires_at": "2024-12-31T23:59:59Z"
}

响应:
{
  "code": 201,
  "message": "密码条目创建成功",
  "data": {
    "id": 2,
    "title": "测试服务器",
    "username": "root",
    "host": "test.example.com",
    "port": 22,
    "account_type": "server",
    "password_strength": 65,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

#### 3.4.3 获取密码详情
```
GET /api/passwords/1
Authorization: Bearer {access_token}

响应:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "生产数据库",
    "username": "admin",
    "password": "decrypted_password",
    "url": "mysql://prod.example.com",
    "host": "prod.example.com",
    "port": 3306,
    "database_name": "production",
    "connection_type": "mysql",
    "account_type": "database",
    "notes": "生产环境主数据库",
    "custom_fields": {
      "责任人": "李四",
      "备份时间": "每日凌晨2点"
    },
    "category": {
      "id": 1,
      "name": "数据库",
      "color": "#1890ff"
    },
    "tags": [
      {
        "id": 1,
        "name": "生产环境",
        "color": "#f50"
      }
    ],
    "password_strength": 85,
    "is_favorite": false,
    "expires_at": "2024-12-31T23:59:59Z",
    "created_by": {
      "id": 1,
      "username": "admin",
      "first_name": "管理员"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z",
    "last_accessed": "2024-01-01T10:00:00Z"
  }
}
```

#### 3.4.4 生成随机密码
```
POST /api/passwords/generate
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "length": 16,
  "include_uppercase": true,
  "include_lowercase": true,
  "include_numbers": true,
  "include_symbols": true,
  "exclude_ambiguous": true
}

响应:
{
  "code": 200,
  "message": "密码生成成功",
  "data": {
    "password": "Kp9#mN2$vB8@xL5!",
    "strength": 95,
    "entropy": 104.2
  }
}
```

### 3.5 分类和标签API

#### 3.5.1 获取分类列表
```
GET /api/categories
Authorization: Bearer {access_token}

响应:
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "数据库",
      "description": "数据库相关账号",
      "color": "#1890ff",
      "icon": "database",
      "parent_id": null,
      "children": [
        {
          "id": 2,
          "name": "MySQL",
          "color": "#52c41a",
          "parent_id": 1
        }
      ],
      "password_count": 5,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### 3.5.2 创建分类
```
POST /api/categories
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "name": "Web应用",
  "description": "Web应用相关账号",
  "color": "#722ed1",
  "icon": "global",
  "parent_id": null
}

响应:
{
  "code": 201,
  "message": "分类创建成功",
  "data": {
    "id": 3,
    "name": "Web应用",
    "description": "Web应用相关账号",
    "color": "#722ed1",
    "icon": "global",
    "parent_id": null,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 3.6 分享功能API

#### 3.6.1 创建分享链接
```
POST /api/passwords/1/share
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "share_type": "one_time",
  "permission": "read",
  "expires_at": "2024-01-02T00:00:00Z",
  "max_views": 1,
  "shared_to_email": "<EMAIL>"
}

响应:
{
  "code": 201,
  "message": "分享链接创建成功",
  "data": {
    "share_token": "abc123def456",
    "share_url": "https://password.example.com/shared/abc123def456",
    "expires_at": "2024-01-02T00:00:00Z",
    "max_views": 1,
    "view_count": 0
  }
}
```

#### 3.6.2 通过分享链接查看密码
```
GET /api/shared/abc123def456

响应:
{
  "code": 200,
  "message": "success",
  "data": {
    "title": "生产数据库",
    "username": "admin",
    "password": "decrypted_password",
    "host": "prod.example.com",
    "port": 3306,
    "notes": "生产环境主数据库",
    "shared_by": "张三",
    "remaining_views": 0,
    "expires_at": "2024-01-02T00:00:00Z"
  }
}
```

### 3.7 管理员API

#### 3.7.1 用户管理
```
GET /api/admin/users?page=1&page_size=20&search=zhang&department=1
Authorization: Bearer {access_token}
X-Admin-Required: true

响应:
{
  "code": 200,
  "message": "success",
  "data": {
    "results": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "username": "zhang",
        "first_name": "张",
        "last_name": "三",
        "is_active": true,
        "is_staff": false,
        "department": {
          "id": 1,
          "name": "技术部"
        },
        "roles": ["user"],
        "last_login": "2024-01-01T10:00:00Z",
        "date_joined": "2024-01-01T00:00:00Z",
        "password_count": 5
      }
    ],
    "count": 1,
    "page": 1,
    "page_size": 20,
    "total_pages": 1
  }
}
```

#### 3.7.2 创建用户
```
POST /api/admin/users
Authorization: Bearer {access_token}
X-Admin-Required: true
Content-Type: application/json

请求体:
{
  "email": "<EMAIL>",
  "username": "newuser",
  "first_name": "新",
  "last_name": "用户",
  "password": "temppass123",
  "department_id": 1,
  "role_ids": [1]
}

响应:
{
  "code": 201,
  "message": "用户创建成功",
  "data": {
    "id": 2,
    "email": "<EMAIL>",
    "username": "newuser",
    "first_name": "新",
    "last_name": "用户",
    "is_active": true,
    "department": {
      "id": 1,
      "name": "技术部"
    },
    "roles": ["user"],
    "date_joined": "2024-01-01T12:00:00Z"
  }
}
```

## 4. 前端页面设计

### 4.1 整体布局设计

#### 4.1.1 主布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏                              │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│             │                                           │
│   侧边栏     │              主内容区域                     │
│             │                                           │
│             │                                           │
└─────────────┴───────────────────────────────────────────┘
```

#### 4.1.2 顶部导航栏组件
- **左侧**: Logo + 系统名称
- **中间**: 全局搜索框
- **右侧**: 通知图标 + 用户头像下拉菜单

#### 4.1.3 侧边栏菜单
```
仪表盘
密码管理
  ├─ 所有密码
  ├─ 收藏夹
  ├─ 最近访问
  └─ 回收站
分类管理
标签管理
分享管理
  ├─ 我的分享
  └─ 分享给我
系统管理 (管理员)
  ├─ 用户管理
  ├─ 部门管理
  ├─ 角色权限
  ├─ 操作日志
  └─ 系统设置
个人设置
  ├─ 个人信息
  ├─ 安全设置
  └─ 偏好设置
```

### 4.2 登录页面设计

#### 4.2.1 页面布局
```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│    ┌─────────────┐              ┌─────────────────┐     │
│    │             │              │                 │     │
│    │   Logo +    │              │   登录表单       │     │
│    │   宣传图片   │              │                 │     │
│    │             │              │                 │     │
│    └─────────────┘              └─────────────────┘     │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 4.2.2 登录表单字段
- 邮箱地址 (必填)
- 密码 (必填)
- MFA验证码 (条件显示)
- 记住我 (复选框)
- 登录按钮
- 忘记密码链接

#### 4.2.3 表单验证规则
```typescript
const loginRules = {
  email: [
    { required: true, message: '请输入邮箱地址' },
    { type: 'email', message: '请输入有效的邮箱地址' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度至少6位' }
  ],
  mfaCode: [
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码' }
  ]
}
```

### 4.3 仪表盘页面设计

#### 4.3.1 页面布局
```
┌─────────────────────────────────────────────────────────┐
│  统计卡片区域                                              │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                        │
│  │总数 │ │收藏 │ │分享 │ │过期 │                        │
│  └─────┘ └─────┘ └─────┘ └─────┘                        │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │   最近访问       │  │        安全提醒              │   │
│  │                 │  │                             │   │
│  └─────────────────┘  └─────────────────────────────┘   │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │   密码强度分析   │  │        操作日志              │   │
│  │                 │  │                             │   │
│  └─────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

#### 4.3.2 统计卡片组件
```vue
<template>
  <div class="stats-cards">
    <a-row :gutter="16">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="总密码数"
            :value="stats.total"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <icon-font type="icon-password" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <!-- 其他统计卡片 -->
    </a-row>
  </div>
</template>
```

### 4.4 密码列表页面设计

#### 4.4.1 页面布局
```
┌─────────────────────────────────────────────────────────┐
│  工具栏                                                   │
│  [新建] [导入] [导出] [批量操作]    [搜索框] [筛选] [视图]  │
├─────────────────────────────────────────────────────────┤
│  ┌─────────┐                                             │
│  │ 分类树   │              密码列表/卡片视图                │
│  │         │                                             │
│  │         │                                             │
│  └─────────┘                                             │
└─────────────────────────────────────────────────────────┘
```

#### 4.4.2 密码列表表格列定义
```typescript
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    sorter: true,
    ellipsis: true,
    customRender: ({ record }) => (
      <div class="password-title">
        <icon-font type={getAccountTypeIcon(record.account_type)} />
        <span>{record.title}</span>
        {record.is_favorite && <star-filled class="favorite-icon" />}
      </div>
    )
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    ellipsis: true
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    customRender: ({ record }) => (
      <a-tag color={record.category?.color}>
        {record.category?.name}
      </a-tag>
    )
  },
  {
    title: '标签',
    dataIndex: 'tags',
    key: 'tags',
    customRender: ({ record }) => (
      <div>
        {record.tags?.map(tag => (
          <a-tag key={tag.id} color={tag.color} size="small">
            {tag.name}
          </a-tag>
        ))}
      </div>
    )
  },
  {
    title: '密码强度',
    dataIndex: 'password_strength',
    key: 'password_strength',
    sorter: true,
    customRender: ({ record }) => (
      <a-progress
        percent={record.password_strength}
        size="small"
        status={getPasswordStrengthStatus(record.password_strength)}
      />
    )
  },
  {
    title: '最后访问',
    dataIndex: 'last_accessed',
    key: 'last_accessed',
    sorter: true,
    customRender: ({ text }) => formatRelativeTime(text)
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    customRender: ({ record }) => (
      <a-space>
        <a-button type="link" size="small" onClick={() => viewPassword(record.id)}>
          查看
        </a-button>
        <a-dropdown>
          <a-button type="link" size="small">
            更多 <down-outlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item onClick={() => editPassword(record.id)}>编辑</a-menu-item>
              <a-menu-item onClick={() => sharePassword(record.id)}>分享</a-menu-item>
              <a-menu-item onClick={() => copyPassword(record.id)}>复制密码</a-menu-item>
              <a-menu-divider />
              <a-menu-item danger onClick={() => deletePassword(record.id)}>删除</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-space>
    )
  }
]
```

#### 4.4.3 高级搜索组件
```vue
<template>
  <a-card class="search-card" size="small">
    <a-form layout="inline" :model="searchForm">
      <a-form-item label="关键词">
        <a-input
          v-model:value="searchForm.keyword"
          placeholder="搜索标题、用户名、备注"
          allow-clear
        >
          <template #prefix>
            <search-outlined />
          </template>
        </a-input>
      </a-form-item>
      
      <a-form-item label="账号类型">
        <a-select
          v-model:value="searchForm.account_type"
          placeholder="选择账号类型"
          allow-clear
          style="width: 120px"
        >
          <a-select-option value="server">服务器</a-select-option>
          <a-select-option value="database">数据库</a-select-option>
          <a-select-option value="website">网站</a-select-option>
          <a-select-option value="application">应用</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="分类">
        <a-tree-select
          v-model:value="searchForm.category_id"
          :tree-data="categoryTree"
          placeholder="选择分类"
          allow-clear
          tree-default-expand-all
        />
      </a-form-item>
      
      <a-form-item label="标签">
        <a-select
          v-model:value="searchForm.tag_ids"
          mode="multiple"
          placeholder="选择标签"
          allow-clear
          style="width: 200px"
        >
          <a-select-option
            v-for="tag in tags"
            :key="tag.id"
            :value="tag.id"
          >
            <a-tag :color="tag.color" size="small">{{ tag.name }}</a-tag>
          </a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item>
        <a-space>
          <a-button type="primary" @click="handleSearch">
            <search-outlined /> 搜索
          </a-button>
          <a-button @click="handleReset">
            重置
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-card>
</template>
```

### 4.5 密码详情页面设计

#### 4.5.1 页面布局
```
┌─────────────────────────────────────────────────────────┐
│  页面头部                                                 │
│  [返回] 密码标题                    [编辑] [分享] [删除]    │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │   基本信息       │  │        安全信息              │   │
│  │                 │  │                             │   │
│  └─────────────────┘  └─────────────────────────────┘   │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │   自定义字段     │  │        操作历史              │   │
│  │                 │  │                             │   │
│  └─────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

#### 4.5.2 密码显示组件
```vue
<template>
  <div class="password-field">
    <a-form-item label="密码">
      <a-input-group compact>
        <a-input
          :value="showPassword ? password : '••••••••••••'"
          readonly
          style="width: calc(100% - 80px)"
        />
        <a-button @click="togglePasswordVisibility">
          <eye-outlined v-if="!showPassword" />
          <eye-invisible-outlined v-else />
        </a-button>
        <a-button @click="copyPassword">
          <copy-outlined />
        </a-button>
      </a-input-group>
    </a-form-item>
    
    <a-form-item label="密码强度">
      <a-progress
        :percent="passwordStrength"
        :status="getPasswordStrengthStatus(passwordStrength)"
      />
      <div class="strength-tips">
        <a-tag
          v-for="tip in strengthTips"
          :key="tip"
          color="orange"
          size="small"
        >
          {{ tip }}
        </a-tag>
      </div>
    </a-form-item>
  </div>
</template>
```

### 4.6 密码编辑页面设计

#### 4.6.1 表单布局
```vue
<template>
  <a-form
    :model="form"
    :rules="rules"
    layout="vertical"
    @finish="handleSubmit"
  >
    <a-row :gutter="24">
      <a-col :span="12">
        <a-card title="基本信息" size="small">
          <a-form-item label="标题" name="title">
            <a-input v-model:value="form.title" placeholder="请输入密码条目标题" />
          </a-form-item>
          
          <a-form-item label="账号类型" name="account_type">
            <a-select v-model:value="form.account_type" placeholder="选择账号类型">
              <a-select-option value="server">服务器</a-select-option>
              <a-select-option value="database">数据库</a-select-option>
              <a-select-option value="website">网站</a-select-option>
              <a-select-option value="application">应用</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="用户名" name="username">
            <a-input v-model:value="form.username" placeholder="请输入用户名" />
          </a-form-item>
          
          <a-form-item label="密码" name="password">
            <a-input-group compact>
              <a-input
                v-model:value="form.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                style="width: calc(100% - 120px)"
              />
              <a-button @click="togglePasswordVisibility">
                <eye-outlined v-if="!showPassword" />
                <eye-invisible-outlined v-else />
              </a-button>
              <a-button @click="generatePassword">
                <reload-outlined /> 生成
              </a-button>
            </a-input-group>
            <div class="password-strength">
              <a-progress
                :percent="passwordStrength"
                size="small"
                :status="getPasswordStrengthStatus(passwordStrength)"
              />
            </div>
          </a-form-item>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="连接信息" size="small">
          <a-form-item label="主机地址" name="host">
            <a-input v-model:value="form.host" placeholder="请输入主机地址" />
          </a-form-item>
          
          <a-form-item label="端口" name="port">
            <a-input-number
              v-model:value="form.port"
              :min="1"
              :max="65535"
              placeholder="端口号"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="URL" name="url">
            <a-input v-model:value="form.url" placeholder="请输入完整URL" />
          </a-form-item>
          
          <a-form-item label="数据库名" name="database_name" v-if="form.account_type === 'database'">
            <a-input v-model:value="form.database_name" placeholder="请输入数据库名" />
          </a-form-item>
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="24">
      <a-col :span="12">
        <a-card title="分类和标签" size="small">
          <a-form-item label="分类" name="category_id">
            <a-tree-select
              v-model:value="form.category_id"
              :tree-data="categoryTree"
              placeholder="选择分类"
              allow-clear
              tree-default-expand-all
            />
          </a-form-item>
          
          <a-form-item label="标签" name="tag_ids">
            <a-select
              v-model:value="form.tag_ids"
              mode="multiple"
              placeholder="选择或创建标签"
              :options="tagOptions"
              @search="handleTagSearch"
            />
          </a-form-item>
          
          <a-form-item label="过期时间" name="expires_at">
            <a-date-picker
              v-model:value="form.expires_at"
              show-time
              placeholder="选择过期时间"
              style="width: 100%"
            />
          </a-form-item>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="备注信息" size="small">
          <a-form-item label="备注" name="notes">
            <a-textarea
              v-model:value="form.notes"
              :rows="4"
              placeholder="请输入备注信息"
            />
          </a-form-item>
          
          <a-form-item label="自定义字段">
            <div v-for="(field, index) in form.custom_fields" :key="index" class="custom-field">
              <a-input-group compact>
                <a-input
                  v-model:value="field.key"
                  placeholder="字段名"
                  style="width: 30%"
                />
                <a-input
                  v-model:value="field.value"
                  placeholder="字段值"
                  style="width: 60%"
                />
                <a-button
                  type="text"
                  danger
                  @click="removeCustomField(index)"
                  style="width: 10%"
                >
                  <delete-outlined />
                </a-button>
              </a-input-group>
            </div>
            <a-button type="dashed" @click="addCustomField" block>
              <plus-outlined /> 添加自定义字段
            </a-button>
          </a-form-item>
        </a-card>
      </a-col>
    </a-row>
    
    <a-form-item>
      <a-space>
        <a-button type="primary" html-type="submit" :loading="loading">
          保存
        </a-button>
        <a-button @click="handleCancel">
          取消
        </a-button>
      </a-space>
    </a-form-item>
  </a-form>
</template>
```

### 4.7 密码生成器组件

```vue
<template>
  <a-modal
    v-model:visible="visible"
    title="密码生成器"
    width="600px"
    @ok="handleConfirm"
  >
    <div class="password-generator">
      <a-form layout="vertical">
        <a-form-item label="生成的密码">
          <a-input-group compact>
            <a-input
              :value="generatedPassword"
              readonly
              style="width: calc(100% - 80px)"
            />
            <a-button @click="copyPassword">
              <copy-outlined />
            </a-button>
            <a-button @click="generatePassword">
              <reload-outlined />
            </a-button>
          </a-input-group>
          <div class="password-strength">
            <a-progress
              :percent="passwordStrength"
              :status="getPasswordStrengthStatus(passwordStrength)"
            />
            <span class="strength-text">强度: {{ getPasswordStrengthText(passwordStrength) }}</span>
          </div>
        </a-form-item>
        
        <a-form-item label="密码长度">
          <a-slider
            v-model:value="options.length"
            :min="6"
            :max="64"
            :marks="{ 6: '6', 16: '16', 32: '32', 64: '64' }"
            @change="generatePassword"
          />
        </a-form-item>
        
        <a-form-item label="字符类型">
          <a-checkbox-group v-model:value="characterTypes" @change="generatePassword">
            <a-row>
              <a-col :span="12">
                <a-checkbox value="uppercase">大写字母 (A-Z)</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="lowercase">小写字母 (a-z)</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="numbers">数字 (0-9)</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="symbols">特殊字符 (!@#$%)</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="options.exclude_ambiguous" @change="generatePassword">
            排除易混淆字符 (0, O, l, 1, I)
          </a-checkbox>
        </a-form-item>
        
        <a-form-item label="排除字符">
          <a-input
            v-model:value="options.exclude_chars"
            placeholder="输入要排除的字符"
            @change="generatePassword"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
```

### 4.8 分享功能页面设计

#### 4.8.1 分享对话框
```vue
<template>
  <a-modal
    v-model:visible="visible"
    title="分享密码"
    width="500px"
    @ok="handleShare"
  >
    <a-form :model="shareForm" layout="vertical">
      <a-form-item label="分享类型">
        <a-radio-group v-model:value="shareForm.share_type">
          <a-radio value="user">分享给用户</a-radio>
          <a-radio value="one_time">一次性链接</a-radio>
          <a-radio value="temporary">临时链接</a-radio>
        </a-radio-group>
      </a-form-item>
      
      <a-form-item label="接收者" v-if="shareForm.share_type === 'user'">
        <a-select
          v-model:value="shareForm.shared_to"
          show-search
          placeholder="选择用户"
          :filter-option="filterUser"
        >
          <a-select-option
            v-for="user in users"
            :key="user.id"
            :value="user.id"
          >
            {{ user.first_name }} {{ user.last_name }} ({{ user.email }})
          </a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="权限">
        <a-radio-group v-model:value="shareForm.permission">
          <a-radio value="read">只读</a-radio>
          <a-radio value="edit">可编辑</a-radio>
        </a-radio-group>
      </a-form-item>
      
      <a-form-item label="过期时间">
        <a-date-picker
          v-model:value="shareForm.expires_at"
          show-time
          placeholder="选择过期时间"
          style="width: 100%"
        />
      </a-form-item>
      
     <a-form-item label="最大查看次数" v-if="shareForm.share_type !== 'user'">
        <a-input-number
          v-model:value="shareForm.max_views"
          :min="1"
          :max="100"
          placeholder="最大查看次数"
          style="width: 100%"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
```

### 4.9 管理员页面设计

#### 4.9.1 用户管理页面
```vue
<template>
  <div class="user-management">
    <a-card>
      <template #title>
        <a-space>
          <user-outlined />
          用户管理
        </a-space>
      </template>
      
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showCreateUser">
            <plus-outlined /> 新建用户
          </a-button>
          <a-button @click="exportUsers">
            <export-outlined /> 导出
          </a-button>
        </a-space>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <a-form layout="inline" :model="searchForm">
          <a-form-item>
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="搜索用户名、邮箱"
              allow-clear
            >
              <template #prefix>
                <search-outlined />
              </template>
            </a-input>
          </a-form-item>
          
          <a-form-item>
            <a-select
              v-model:value="searchForm.department_id"
              placeholder="选择部门"
              allow-clear
              style="width: 150px"
            >
              <a-select-option
                v-for="dept in departments"
                :key="dept.id"
                :value="dept.id"
              >
                {{ dept.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item>
            <a-select
              v-model:value="searchForm.status"
              placeholder="用户状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="active">激活</a-select-option>
              <a-select-option value="inactive">禁用</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              搜索
            </a-button>
          </a-form-item>
        </a-form>
      </div>
      
      <!-- 用户表格 -->
      <a-table
        :columns="userColumns"
        :data-source="users"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar :src="record.avatar" :size="32">
              {{ record.first_name?.charAt(0) }}
            </a-avatar>
          </template>
          
          <template v-if="column.key === 'status'">
            <a-tag :color="record.is_active ? 'green' : 'red'">
              {{ record.is_active ? '激活' : '禁用' }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'roles'">
            <a-tag
              v-for="role in record.roles"
              :key="role"
              color="blue"
            >
              {{ role }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="editUser(record)">
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                :danger="record.is_active"
                @click="toggleUserStatus(record)"
              >
                {{ record.is_active ? '禁用' : '启用' }}
              </a-button>
              <a-button type="link" size="small" @click="resetPassword(record)">
                重置密码
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>
```

## 5. 安全设计详细规范

### 5.1 加密算法实现

#### 5.1.1 主密码处理
```python
# utils/encryption.py
import hashlib
import secrets
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from gmssl import sm4

class PasswordEncryption:
    def __init__(self):
        self.iterations = 100000  # PBKDF2迭代次数
        
    def derive_key_from_master_password(self, master_password: str, salt: bytes) -> bytes:
        """从主密码派生加密密钥"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=16,  # SM4需要16字节密钥
            salt=salt,
            iterations=self.iterations,
        )
        return kdf.derive(master_password.encode('utf-8'))
    
    def encrypt_password(self, password: str, master_password: str) -> dict:
        """使用SM4算法加密密码"""
        # 生成随机盐
        salt = secrets.token_bytes(16)
        
        # 从主密码派生密钥
        key = self.derive_key_from_master_password(master_password, salt)
        
        # 使用SM4加密
        sm4_cipher = sm4.CryptSM4()
        sm4_cipher.set_key(key, sm4.SM4_ENCRYPT)
        
        # 填充密码到16字节的倍数
        padded_password = self._pad_data(password.encode('utf-8'))
        encrypted_data = sm4_cipher.crypt_ecb(padded_password)
        
        return {
            'encrypted_data': encrypted_data.hex(),
            'salt': salt.hex(),
            'algorithm': 'SM4-ECB'
        }
    
    def decrypt_password(self, encrypted_data: str, salt: str, master_password: str) -> str:
        """解密密码"""
        # 从主密码派生密钥
        key = self.derive_key_from_master_password(
            master_password, 
            bytes.fromhex(salt)
        )
        
        # 使用SM4解密
        sm4_cipher = sm4.CryptSM4()
        sm4_cipher.set_key(key, sm4.SM4_DECRYPT)
        
        decrypted_data = sm4_cipher.crypt_ecb(bytes.fromhex(encrypted_data))
        
        # 去除填充
        return self._unpad_data(decrypted_data).decode('utf-8')
    
    def _pad_data(self, data: bytes) -> bytes:
        """PKCS7填充"""
        block_size = 16
        padding_length = block_size - (len(data) % block_size)
        padding = bytes([padding_length] * padding_length)
        return data + padding
    
    def _unpad_data(self, data: bytes) -> bytes:
        """去除PKCS7填充"""
        padding_length = data[-1]
        return data[:-padding_length]
```

#### 5.1.2 JWT认证实现
```python
# utils/authentication.py
import jwt
import datetime
from django.conf import settings
from django.contrib.auth import get_user_model

User = get_user_model()

class JWTAuthentication:
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = 'HS256'
        self.access_token_lifetime = datetime.timedelta(hours=1)
        self.refresh_token_lifetime = datetime.timedelta(days=7)
    
    def generate_tokens(self, user):
        """生成访问令牌和刷新令牌"""
        now = datetime.datetime.utcnow()
        
        # 访问令牌载荷
        access_payload = {
            'user_id': user.id,
            'email': user.email,
            'type': 'access',
            'iat': now,
            'exp': now + self.access_token_lifetime
        }
        
        # 刷新令牌载荷
        refresh_payload = {
            'user_id': user.id,
            'type': 'refresh',
            'iat': now,
            'exp': now + self.refresh_token_lifetime
        }
        
        access_token = jwt.encode(access_payload, self.secret_key, algorithm=self.algorithm)
        refresh_token = jwt.encode(refresh_payload, self.secret_key, algorithm=self.algorithm)
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'expires_in': int(self.access_token_lifetime.total_seconds())
        }
    
    def verify_token(self, token, token_type='access'):
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            if payload.get('type') != token_type:
                raise jwt.InvalidTokenError('Invalid token type')
            
            user = User.objects.get(id=payload['user_id'])
            return user, payload
            
        except (jwt.ExpiredSignatureError, jwt.InvalidTokenError, User.DoesNotExist):
            return None, None
```

### 5.2 权限控制系统

#### 5.2.1 权限定义
```python
# apps/users/permissions.py
from rest_framework import permissions
from django.contrib.auth.models import Permission

class PasswordPermissions:
    """密码相关权限"""
    VIEW_PASSWORD = 'view_password'
    ADD_PASSWORD = 'add_password'
    CHANGE_PASSWORD = 'change_password'
    DELETE_PASSWORD = 'delete_password'
    SHARE_PASSWORD = 'share_password'
    
class AdminPermissions:
    """管理员权限"""
    MANAGE_USERS = 'manage_users'
    MANAGE_DEPARTMENTS = 'manage_departments'
    MANAGE_ROLES = 'manage_roles'
    VIEW_LOGS = 'view_logs'
    MANAGE_SETTINGS = 'manage_settings'

class IsPasswordOwnerOrShared(permissions.BasePermission):
    """检查用户是否为密码所有者或被分享者"""
    
    def has_object_permission(self, request, view, obj):
        # 密码创建者有完全权限
        if obj.created_by == request.user:
            return True
        
        # 检查是否有分享权限
        from apps.sharing.models import ShareRecord
        shared_records = ShareRecord.objects.filter(
            password_entry=obj,
            shared_to=request.user,
            is_active=True
        )
        
        if shared_records.exists():
            share_record = shared_records.first()
            
            # 检查是否过期
            if share_record.expires_at and share_record.expires_at < timezone.now():
                return False
            
            # 检查查看次数限制
            if share_record.max_views and share_record.view_count >= share_record.max_views:
                return False
            
            # 检查权限类型
            if request.method in permissions.SAFE_METHODS:
                return True
            elif share_record.permission == 'edit':
                return True
        
        return False
```

### 5.3 审计日志系统

#### 5.3.1 日志记录中间件
```python
# utils/middleware.py
import json
import time
from django.utils.deprecation import MiddlewareMixin
from apps.audit.models import OperationLog

class AuditLogMiddleware(MiddlewareMixin):
    """审计日志中间件"""
    
    def process_request(self, request):
        request._audit_start_time = time.time()
        return None
    
    def process_response(self, request, response):
        # 只记录API请求
        if not request.path.startswith('/api/'):
            return response
        
        # 获取用户信息
        user = getattr(request, 'user', None)
        if user and user.is_authenticated:
            user_id = user.id
        else:
            user_id = None
        
        # 确定操作类型
        action = self._get_action_from_request(request)
        
        # 获取资源信息
        resource_type, resource_id = self._get_resource_info(request)
        
        # 记录日志
        OperationLog.objects.create(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=self._get_request_details(request),
            ip_address=self._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            success=200 <= response.status_code < 400,
            error_message=self._get_error_message(response) if response.status_code >= 400 else None
        )
        
        return response
    
    def _get_action_from_request(self, request):
        """从请求中确定操作类型"""
        method_action_map = {
            'GET': 'view',
            'POST': 'create',
            'PUT': 'update',
            'PATCH': 'update',
            'DELETE': 'delete'
        }
        return method_action_map.get(request.method, 'unknown')
    
    def _get_resource_info(self, request):
        """从URL中提取资源信息"""
        path_parts = request.path.strip('/').split('/')
        
        if len(path_parts) >= 3:
            resource_type = path_parts[2]  # /api/v1/passwords -> passwords
            
            # 尝试提取资源ID
            if len(path_parts) >= 4 and path_parts[3].isdigit():
                resource_id = int(path_parts[3])
            else:
                resource_id = None
                
            return resource_type, resource_id
        
        return None, None
    
    def _get_request_details(self, request):
        """获取请求详情"""
        details = {
            'method': request.method,
            'path': request.path,
            'query_params': dict(request.GET)
        }
        
        # 记录POST/PUT数据（排除敏感信息）
        if request.method in ['POST', 'PUT', 'PATCH']:
            try:
                body = json.loads(request.body.decode('utf-8'))
                # 移除敏感字段
                sensitive_fields = ['password', 'master_password', 'token']
                for field in sensitive_fields:
                    if field in body:
                        body[field] = '***'
                details['body'] = body
            except:
                pass
        
        return details
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
```

## 6. 前端状态管理设计

### 6.1 Pinia Store结构

#### 6.1.1 用户状态管理
```typescript
// stores/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, UserProfile } from '@/types/user'
import { authApi } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')n  const permissions = ref<string[]>([])
  const isLoggedIn = computed(() => !!token.value)
  
  // 登录
  const login = async (loginForm: LoginForm) => {
    try {
      const response = await authApi.login(loginForm)
      const { access_token, refresh_token, user: userInfo } = response.data
      
      token.value = access_token
      refreshToken.value = refresh_token
      user.value = userInfo
      permissions.value = userInfo.permissions
      
      // 保存到localStorage
      localStorage.setItem('access_token', access_token)
      localStorage.setItem('refresh_token', refresh_token)
      
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } finally {
      // 清除状态
      user.value = null
      token.value = ''
      refreshToken.value = ''
      permissions.value = []
      
      // 清除localStorage
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }
  
  // 刷新token
  const refreshAccessToken = async () => {
    try {
      const response = await authApi.refreshToken(refreshToken.value)
      token.value = response.data.access_token
      localStorage.setItem('access_token', response.data.access_token)
      return response.data.access_token
    } catch (error) {
      // 刷新失败，重新登录
      await logout()
      throw error
    }
  }
  
  // 检查权限
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission)
  }
  
  // 初始化用户信息
  const initUser = async () => {
    const savedToken = localStorage.getItem('access_token')
    if (savedToken) {
      token.value = savedToken
      refreshToken.value = localStorage.getItem('refresh_token') || ''
      
      try {
        const response = await authApi.getProfile()
        user.value = response.data
        permissions.value = response.data.permissions
      } catch (error) {
        // token无效，清除
        await logout()
      }
    }
  }
  
  return {
    user,
    token,
    refreshToken,
    permissions,
    isLoggedIn,
    login,
    logout,
    refreshAccessToken,
    hasPermission,
    initUser
  }
})
```

#### 6.1.2 密码管理状态
```typescript
// stores/password.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { PasswordEntry, PasswordFilter, Category, Tag } from '@/types/password'
import { passwordApi } from '@/api/password'

export const usePasswordStore = defineStore('password', () => {
  // 状态
  const passwords = ref<PasswordEntry[]>([])
  const categories = ref<Category[]>([])
  const tags = ref<Tag[]>([])
  const loading = ref(false)
  const filter = ref<PasswordFilter>({
    keyword: '',
    category_id: null,
    tag_ids: [],
    account_type: null
  })
  
  // 计算属性
  const filteredPasswords = computed(() => {
    let result = passwords.value
    
    if (filter.value.keyword) {
      const keyword = filter.value.keyword.toLowerCase()
      result = result.filter(p => 
        p.title.toLowerCase().includes(keyword) ||
        p.username?.toLowerCase().includes(keyword) ||
        p.notes?.toLowerCase().includes(keyword)
      )
    }
    
    if (filter.value.category_id) {
      result = result.filter(p => p.category?.id === filter.value.category_id)
    }
    
    if (filter.value.tag_ids?.length) {
      result = result.filter(p => 
        p.tags?.some(tag => filter.value.tag_ids!.includes(tag.id))
      )
    }
    
    if (filter.value.account_type) {
      result = result.filter(p => p.account_type === filter.value.account_type)
    }
    
    return result
  })
  
  // 获取密码列表
  const fetchPasswords = async (params?: any) => {
    loading.value = true
    try {
      const response = await passwordApi.getList(params)
      passwords.value = response.data.results
      return response.data
    } finally {
      loading.value = false
    }
  }
  
  // 创建密码
  const createPassword = async (data: Partial<PasswordEntry>) => {
    const response = await passwordApi.create(data)
    passwords.value.unshift(response.data)
    return response.data
  }
  
  // 更新密码
  const updatePassword = async (id: number, data: Partial<PasswordEntry>) => {
    const response = await passwordApi.update(id, data)
    const index = passwords.value.findIndex(p => p.id === id)
    if (index !== -1) {
      passwords.value[index] = response.data
    }
    return response.data
  }
  
  // 删除密码
  const deletePassword = async (id: number) => {
    await passwordApi.delete(id)
    const index = passwords.value.findIndex(p => p.id === id)
    if (index !== -1) {
      passwords.value.splice(index, 1)
    }
  }
  
  // 获取分类列表
  const fetchCategories = async () => {
    const response = await passwordApi.getCategories()
    categories.value = response.data
    return response.data
  }
  
  // 获取标签列表
  const fetchTags = async () => {
    const response = await passwordApi.getTags()
    tags.value = response.data
    return response.data
  }
  
  // 设置筛选条件
  const setFilter = (newFilter: Partial<PasswordFilter>) => {
    filter.value = { ...filter.value, ...newFilter }
  }
  
  // 清除筛选条件
  const clearFilter = () => {
    filter.value = {
      keyword: '',
      category_id: null,
      tag_ids: [],
      account_type: null
    }
  }
  
  return {
    passwords,
    categories,
    tags,
    loading,
    filter,
    filteredPasswords,
    fetchPasswords,
    createPassword,
    updatePassword,
    deletePassword,
    fetchCategories,
    fetchTags,
    setFilter,
    clearFilter
  }
})
```

## 7. 部署配置

### 7.1 Docker配置

#### 7.1.1 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "config.wsgi:application"]
```

#### 7.1.2 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
```

#### 7.1.3 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: locker_mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password
    
  redis:
    image: redis:7-alpine
    container_name: locker_redis
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
      
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: locker_backend
    environment:
      - DEBUG=False
      - DATABASE_URL=mysql://${MYSQL_USER}:${MYSQL_PASSWORD}@mysql:3306/${MYSQL_DATABASE}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./backend/media:/app/media
      - ./backend/logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - redis
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn --bind 0.0.0.0:8000 config.wsgi:application"
             
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: locker_frontend
    ports:
      - "80:80"
    depends_on:
      - backend
      
volumes:
  mysql_data:
  redis_data:
```

### 7.2 环境配置

#### 7.2.1 环境变量配置
```bash
# .env
# 数据库配置
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_DATABASE=locker
MYSQL_USER=locker_user
MYSQL_PASSWORD=your_password

# Django配置
SECRET_KEY=your_secret_key
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# 邮件配置
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password
EMAIL_USE_TLS=True

# 安全配置
CSRF_TRUSTED_ORIGINS=https://your-domain.com
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

## 8. 总结

本设计文档详细描述了密码管理系统的技术实现方案，包括：

1. **数据库设计**：完整的表结构设计，支持用户管理、密码存储、分类标签、分享功能等
2. **API设计**：RESTful API接口规范，包含认证、用户管理、密码管理等所有功能模块
3. **前端设计**：基于Vue.js的页面布局和组件设计，提供良好的用户体验
4. **安全设计**：SM4加密算法、JWT认证、权限控制、审计日志等安全机制
5. **状态管理**：Pinia状态管理方案，实现前端数据的统一管理
6. **部署方案**：Docker容器化部署配置，支持生产环境部署

该设计文档为AI智能体提供了完整的技术实现指导，可以根据此文档进行系统开发。