"""
初始化密码组数据的管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordEntryGroup, GroupPermission

User = get_user_model()


class Command(BaseCommand):
    help = "初始化密码组数据"

    def add_arguments(self, parser):
        parser.add_argument(
            '--admin-username',
            type=str,
            default='admin',
            help='管理员用户名 (默认: admin)'
        )

    def handle(self, *args, **options):
        admin_username = options['admin_username']
        
        try:
            admin_user = User.objects.get(username=admin_username)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'用户 {admin_username} 不存在')
            )
            return

        # 创建示例组
        groups_data = [
            {
                'name': '开发团队',
                'description': '开发团队的密码组，包含开发环境相关的密码'
            },
            {
                'name': '测试团队',
                'description': '测试团队的密码组，包含测试环境相关的密码'
            },
            {
                'name': '运维团队',
                'description': '运维团队的密码组，包含生产环境相关的密码'
            },
            {
                'name': '数据库管理',
                'description': '数据库相关的密码组，包含各种数据库的访问密码'
            },
            {
                'name': '云服务',
                'description': '云服务相关的密码组，包含各种云平台的访问密码'
            }
        ]

        created_groups = []
        
        for group_data in groups_data:
            group, created = PasswordEntryGroup.objects.get_or_create(
                name=group_data['name'],
                created_by=admin_user,
                defaults={
                    'description': group_data['description']
                }
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'创建组: {group.name}')
                )
                created_groups.append(group)
            else:
                self.stdout.write(
                    self.style.WARNING(f'组已存在: {group.name}')
                )

            # 为管理员用户创建管理员权限
            permission, perm_created = GroupPermission.objects.get_or_create(
                user=admin_user,
                group=group,
                defaults={
                    'permission': 'admin',
                    'granted_by': admin_user
                }
            )
            
            if perm_created:
                self.stdout.write(
                    self.style.SUCCESS(f'为用户 {admin_user.username} 创建组 {group.name} 的管理员权限')
                )

        if created_groups:
            self.stdout.write(
                self.style.SUCCESS(f'成功创建 {len(created_groups)} 个组')
            )
        else:
            self.stdout.write(
                self.style.WARNING('没有创建新组，所有组都已存在')
            )

        # 显示统计信息
        total_groups = PasswordEntryGroup.objects.filter(is_active=True).count()
        total_permissions = GroupPermission.objects.count()
        
        self.stdout.write(
            self.style.SUCCESS(f'\n统计信息:')
        )
        self.stdout.write(f'- 总组数: {total_groups}')
        self.stdout.write(f'- 总权限数: {total_permissions}')
        
        self.stdout.write(
            self.style.SUCCESS('\n密码组初始化完成!')
        )
