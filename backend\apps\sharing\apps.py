from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class SharingConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.sharing'
    verbose_name = _("密码分享")
    
    def ready(self):
        """应用准备就绪时的初始化操作"""
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
        
        # 启动定时任务清理过期链接
        try:
            from .tasks import start_cleanup_scheduler
            start_cleanup_scheduler()
        except ImportError:
            pass