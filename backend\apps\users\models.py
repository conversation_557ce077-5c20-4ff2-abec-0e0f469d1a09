from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class Department(models.Model):
    """部门模型"""

    name = models.CharField(max_length=100, verbose_name=_("部门名称"))
    description = models.TextField(blank=True, verbose_name=_("部门描述"))
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("上级部门"),
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("部门")
        verbose_name_plural = _("部门")
        db_table = "departments"

    def __str__(self):
        return self.name


class Team(models.Model):
    """团队模型"""

    name = models.CharField(max_length=100, verbose_name=_("团队名称"))
    description = models.TextField(blank=True, verbose_name=_("团队描述"))
    department = models.ForeignKey(
        Department, on_delete=models.CASCADE, verbose_name=_("所属部门")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("团队")
        verbose_name_plural = _("团队")
        db_table = "teams"

    def __str__(self):
        return self.name


class Role(models.Model):
    """角色模型"""

    ROLE_CHOICES = [
        ("admin", _("系统管理员")),
        ("manager", _("部门管理员")),
        ("user", _("普通用户")),
        ("viewer", _("只读用户")),
    ]

    name = models.CharField(
        max_length=50, choices=ROLE_CHOICES, verbose_name=_("角色名称")
    )
    description = models.TextField(blank=True, verbose_name=_("角色描述"))
    permissions = models.JSONField(default=list, verbose_name=_("权限列表"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("角色")
        verbose_name_plural = _("角色")
        db_table = "roles"

    def __str__(self):
        return self.get_name_display()


class User(AbstractUser):
    """用户模型"""

    email = models.EmailField(
        unique=True, blank=True, null=True, verbose_name=_("邮箱")
    )
    name = models.CharField(max_length=100, blank=True, verbose_name=_("姓名"))

    def save(self, *args, **kwargs):
        # 将空字符串转换为None，避免unique约束冲突
        if self.email == "":
            self.email = None
        super().save(*args, **kwargs)

    phone = models.CharField(max_length=20, blank=True, verbose_name=_("手机号"))
    avatar = models.ImageField(
        upload_to="avatars/", blank=True, null=True, verbose_name=_("头像")
    )
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("部门"),
    )
    team = models.ForeignKey(
        Team, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("团队")
    )
    role = models.ForeignKey(
        Role, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("角色")
    )
    home_path = models.CharField(
        max_length=255, default="/passwords", verbose_name=_("首页路径")
    )
    is_mfa_enabled = models.BooleanField(
        default=False, verbose_name=_("启用多因素认证")
    )
    mfa_secret = models.CharField(max_length=32, blank=True, verbose_name=_("MFA密钥"))
    last_password_change = models.DateTimeField(
        null=True, blank=True, verbose_name=_("最后密码修改时间")
    )
    failed_login_attempts = models.IntegerField(
        default=0, verbose_name=_("失败登录次数")
    )
    locked_until = models.DateTimeField(
        null=True, blank=True, verbose_name=_("锁定到期时间")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    USERNAME_FIELD = "username"
    # REQUIRED_FIELDS = ["username"]

    class Meta:
        verbose_name = _("用户")
        verbose_name_plural = _("用户")
        db_table = "users"

    def __str__(self):
        return f"{self.username} ({self.email})"

    @property
    def is_locked(self):
        """检查用户是否被锁定"""
        if self.locked_until:
            from django.utils import timezone

            return timezone.now() < self.locked_until
        return False

    def reset_failed_attempts(self):
        """重置失败登录次数"""
        self.failed_login_attempts = 0
        self.locked_until = None
        self.save(update_fields=["failed_login_attempts", "locked_until"])

    def increment_failed_attempts(self):
        """增加失败登录次数"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:  # 5次失败后锁定30分钟
            from django.utils import timezone
            from datetime import timedelta

            self.locked_until = timezone.now() + timedelta(minutes=30)
        self.save(update_fields=["failed_login_attempts", "locked_until"])
