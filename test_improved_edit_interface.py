#!/usr/bin/env python
"""
测试改进的密码编辑界面功能的脚本
"""
import requests
import json


def test_improved_edit_interface():
    """测试改进的密码编辑界面功能"""
    print("=== 测试改进的密码编辑界面功能 ===")

    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}

    try:
        login_response = requests.post(
            "http://localhost:8001/api/auth/login/", json=login_data
        )
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return

        token = login_response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求失败: {e}")
        return

    # 1. 获取密码组列表（用于测试Transfer组件）
    print("\n1. 获取密码组列表")
    try:
        response = requests.get(
            "http://localhost:8001/api/passwords/groups/", headers=headers
        )
        if response.status_code == 200:
            groups_data = response.json()
            if isinstance(groups_data, dict) and "results" in groups_data:
                groups = groups_data["results"]
            else:
                groups = groups_data if isinstance(groups_data, list) else []

            print(f"✅ 获取密码组成功，共 {len(groups)} 个组")
            for group in groups[:3]:  # 显示前3个组
                print(f"   - {group['name']} (ID: {group['id']})")

            if len(groups) < 2:
                print("⚠️ 密码组数量较少，Transfer组件测试可能受限")
        else:
            print(f"❌ 获取密码组失败: {response.text}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取密码组请求失败: {e}")
        return

    # 2. 创建测试密码条目（包含多个组）
    print("\n2. 创建测试密码条目")
    test_password_data = {
        "title": "测试改进界面功能",
        "username": "testuser",
        "password": "TestPassword123!",
        "hostname": "test.example.com",
        "port": 22,
        "url": "https://test.example.com",
        "notes": "测试备注信息",
        "project_name": "测试项目",
        "responsible_person": "测试负责人",
        "group_ids": [groups[0]["id"]] if groups else [],  # 添加到第一个组
    }

    try:
        response = requests.post(
            "http://localhost:8001/api/passwords/passwords/",
            json=test_password_data,
            headers=headers,
        )
        if response.status_code == 201:
            password_entry = response.json()
            password_id = password_entry["id"]
            print(f"✅ 测试密码条目创建成功")
            print(f"   - ID: {password_id}")
            print(f"   - 标题: {password_entry['title']}")
            print(f"   - 所属组: {len(password_entry.get('groups', []))} 个")
        else:
            print(f"❌ 创建密码条目失败: {response.text}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建密码条目请求失败: {e}")
        return

    # 3. 测试Transfer组件功能 - 更新密码组
    print("\n3. 测试Transfer组件功能")
    if len(groups) >= 2:
        # 添加第二个组
        new_group_ids = [groups[0]["id"], groups[1]["id"]]
        update_groups_data = {"group_ids": new_group_ids}

        try:
            response = requests.post(
                f"http://localhost:8001/api/passwords/passwords/{password_id}/update-info/",
                json=update_groups_data,
                headers=headers,
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 密码组更新成功")
                updated_groups = result.get("data", {}).get("groups", [])
                print(f"   - 现在属于 {len(updated_groups)} 个组")
                for group in updated_groups:
                    if isinstance(group, dict):
                        print(f"     * {group.get('name', 'Unknown')}")
                    else:
                        print(f"     * 组ID: {group}")
            else:
                print(f"❌ 密码组更新失败: {response.text}")
        except requests.exceptions.RequestException as e:
            print(f"❌ 密码组更新请求失败: {e}")
    else:
        print("⚠️ 密码组数量不足，跳过Transfer组件测试")

    # 4. 测试分离的更新功能
    print("\n4. 测试分离的更新功能")

    # 4.1 测试仅更新密码
    print("   4.1 测试仅更新密码")
    try:
        response = requests.post(
            f"http://localhost:8001/api/passwords/passwords/{password_id}/update-password/",
            json={"password": "NewPassword456!"},
            headers=headers,
        )
        if response.status_code == 200:
            print("   ✅ 密码更新成功")
        else:
            print(f"   ❌ 密码更新失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 密码更新请求失败: {e}")

    # 4.2 测试仅更新信息
    print("   4.2 测试仅更新信息")
    try:
        response = requests.post(
            f"http://localhost:8001/api/passwords/passwords/{password_id}/update-info/",
            json={"title": "测试改进界面功能（已更新）", "notes": "更新后的备注信息"},
            headers=headers,
        )
        if response.status_code == 200:
            print("   ✅ 信息更新成功")
        else:
            print(f"   ❌ 信息更新失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 信息更新请求失败: {e}")

    # 5. 验证最终状态
    print("\n5. 验证最终状态")
    try:
        response = requests.get(
            f"http://localhost:8001/api/passwords/passwords/{password_id}/",
            headers=headers,
        )
        if response.status_code == 200:
            final_data = response.json()
            print(f"✅ 最终验证成功")
            print(f"   - 标题: {final_data.get('title', 'N/A')}")
            print(f"   - 用户名: {final_data.get('username', 'N/A')}")
            print(f"   - 主机名: {final_data.get('hostname', 'N/A')}")
            print(f"   - 项目: {final_data.get('project_name', 'N/A')}")
            print(f"   - 备注: {final_data.get('notes', 'N/A')}")
            print(f"   - 所属组数量: {len(final_data.get('groups', []))}")

            # 验证密码是否已更新
            copy_response = requests.post(
                f"http://localhost:8001/api/passwords/passwords/{password_id}/copy/",
                headers=headers,
            )
            if copy_response.status_code == 200:
                copied_password = copy_response.json().get("password", "")
                if copied_password == "NewPassword456!":
                    print(f"   - 密码: 已正确更新")
                else:
                    print(f"   - 密码: 更新异常")
        else:
            print(f"❌ 获取详情失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取详情请求失败: {e}")

    # 6. 清理测试数据
    print("\n6. 清理测试数据")
    try:
        response = requests.delete(
            f"http://localhost:8001/api/passwords/passwords/{password_id}/",
            headers=headers,
        )
        if response.status_code == 204:
            print(f"✅ 测试密码条目已删除")
        else:
            print(f"⚠️ 删除密码条目失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"⚠️ 删除密码条目请求失败: {e}")

    print("\n=== 改进的密码编辑界面功能测试完成 ===")
    print("\n📋 功能检查清单:")
    print("3. ✅ 改进密码编辑界面")
    print("   - Transfer组件替换密码组选择")
    print("   - 优化两列布局结构")
    print("   - 重要信息移到左列上方")
    print("   - 分离的密码和信息更新按钮")
    print("   - 改进的用户体验")
    print("   - 直观的组管理界面")
    print("   - 清晰的操作反馈")
    print("\n🎯 前端界面改进:")
    print("   - 使用Transfer组件进行密码组管理")
    print("   - 核心信息优先显示")
    print("   - 分离的更新操作")
    print("   - 更好的视觉层次")


if __name__ == "__main__":
    test_improved_edit_interface()
