#!/usr/bin/env python
"""
测试密码条目与组集成功能的脚本
"""
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordEntryGroup, GroupPermission, PasswordEntry, PasswordEntryGroupMembership

User = get_user_model()

def test_password_group_integration():
    """测试密码条目与组的集成功能"""
    print("=== 测试密码条目与组的集成功能 ===")
    
    try:
        # 获取admin用户
        admin_user = User.objects.get(username='admin')
        
        # 获取或创建测试组
        group, created = PasswordEntryGroup.objects.get_or_create(
            name="集成测试组",
            created_by=admin_user,
            defaults={
                "description": "用于集成测试的组"
            }
        )
        print(f"使用组: {group.name}")
        
        # 创建组权限
        permission, created = GroupPermission.objects.get_or_create(
            user=admin_user,
            group=group,
            defaults={
                "permission": "admin",
                "granted_by": admin_user
            }
        )
        print(f"组权限: {permission.get_permission_display()}")
        
        # 获取一个现有的密码条目
        password_entries = PasswordEntry.objects.filter(owner=admin_user)[:2]
        
        if not password_entries:
            print("没有找到密码条目，请先创建一些密码条目")
            return
        
        # 将密码条目添加到组中
        for password_entry in password_entries:
            membership, created = PasswordEntryGroupMembership.objects.get_or_create(
                password_entry=password_entry,
                group=group,
                defaults={
                    "added_by": admin_user
                }
            )
            
            if created:
                print(f"将密码条目 '{password_entry.title}' 添加到组 '{group.name}'")
            else:
                print(f"密码条目 '{password_entry.title}' 已在组 '{group.name}' 中")
        
        # 测试组统计信息
        print(f"\n组统计信息:")
        print(f"- 组成员数量: {group.get_members_count()}")
        print(f"- 组密码条目数量: {group.get_password_entries_count()}")
        
        # 测试密码条目的组信息
        for password_entry in password_entries:
            groups = password_entry.groups.all()
            print(f"\n密码条目 '{password_entry.title}' 所属的组:")
            for g in groups:
                print(f"- {g.name}: {g.description}")
        
        print("\n集成测试成功!")
        
    except Exception as e:
        print(f"集成测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_password_api_with_groups():
    """测试带组信息的密码API"""
    print("\n=== 测试带组信息的密码API ===")
    
    base_url = "http://localhost:8001/api/passwords"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取密码列表，检查是否包含组信息
    response = requests.get(f"{base_url}/passwords/", headers=headers)
    print(f"获取密码列表响应: {response.status_code}")
    
    if response.status_code == 200:
        passwords = response.json()
        
        # 查找有组信息的密码条目
        for password in passwords.get('results', []):
            if password.get('groups_data'):
                print(f"\n密码条目: {password['title']}")
                print(f"所属组: {json.dumps(password['groups_data'], indent=2, ensure_ascii=False)}")
                print(f"用户组权限: {json.dumps(password['user_groups_permissions'], indent=2, ensure_ascii=False)}")
                break
        else:
            print("没有找到包含组信息的密码条目")
    else:
        print(f"获取密码列表失败: {response.text}")

if __name__ == "__main__":
    print("开始测试密码条目与组的集成功能...")
    
    # 测试数据库集成
    test_password_group_integration()
    
    # 测试API集成
    test_password_api_with_groups()
