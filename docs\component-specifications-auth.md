# Authentication Component Specifications

## 1. Enhanced Login Component

### 1.1 LoginForm Component
**File**: `frontend/src/components/auth/LoginForm.vue`

#### Props:
```typescript
interface LoginFormProps {
  loading?: boolean;
  showMfaField?: boolean;
  showRememberMe?: boolean;
  showForgotPassword?: boolean;
  initialUsername?: string;
}
```

#### Events:
```typescript
interface LoginFormEvents {
  submit: (credentials: LoginCredentials) => void;
  forgotPassword: () => void;
  mfaRequired: (username: string) => void;
}

interface LoginCredentials {
  username: string;
  password: string;
  mfaCode?: string;
  rememberMe?: boolean;
}
```

#### State Management:
- Form validation using Vben form components
- Real-time password strength indicator
- MFA code auto-formatting (6 digits with spaces)
- Error state handling with specific error types

#### Technical Requirements:
- Integrate with existing Vben form validation
- Support for keyboard navigation (Tab, Enter)
- Auto-focus on first empty field
- Rate limiting feedback for failed attempts
- Accessibility compliance (ARIA labels, screen reader support)

### 1.2 MfaInput Component
**File**: `frontend/src/components/auth/MfaInput.vue`

#### Props:
```typescript
interface MfaInputProps {
  loading?: boolean;
  autoSubmit?: boolean;
  maxLength?: number; // default: 6
  placeholder?: string;
}
```

#### Features:
- 6-digit input with automatic formatting
- Auto-advance between input fields
- Auto-submit when complete (optional)
- Paste support for full codes
- Clear/reset functionality

## 2. MFA Setup Components

### 2.1 MfaSetupWizard Component
**File**: `frontend/src/components/auth/MfaSetupWizard.vue`

#### Props:
```typescript
interface MfaSetupWizardProps {
  userId: string;
  onComplete?: () => void;
  onCancel?: () => void;
}
```

#### Steps:
1. **Introduction Step**: Explain MFA benefits and requirements
2. **QR Code Step**: Display QR code and manual entry option
3. **Verification Step**: Verify authenticator app setup
4. **Backup Codes Step**: Generate and display backup codes

#### State Management:
```typescript
interface MfaSetupState {
  currentStep: number;
  qrCodeUrl: string;
  manualEntryKey: string;
  backupCodes: string[];
  verificationCode: string;
  isVerified: boolean;
}
```

### 2.2 QrCodeDisplay Component
**File**: `frontend/src/components/auth/QrCodeDisplay.vue`

#### Props:
```typescript
interface QrCodeDisplayProps {
  qrCodeUrl: string;
  manualEntryKey: string;
  appName?: string; // default: "Digital Vault"
}
```

#### Features:
- QR code generation using qrcode library
- Manual entry key with copy-to-clipboard
- Responsive design for mobile devices
- Error handling for QR code generation failures

### 2.3 BackupCodes Component
**File**: `frontend/src/components/auth/BackupCodes.vue`

#### Props:
```typescript
interface BackupCodesProps {
  codes: string[];
  onSaved?: () => void;
}
```

#### Features:
- Display backup codes in grid format
- Download as text file
- Copy to clipboard functionality
- Print option
- Confirmation checkbox for "codes saved"

## 3. Password Reset Components

### 3.1 ForgotPasswordForm Component
**File**: `frontend/src/components/auth/ForgotPasswordForm.vue`

#### Props:
```typescript
interface ForgotPasswordFormProps {
  loading?: boolean;
  onSuccess?: () => void;
  onCancel?: () => void;
}
```

#### Features:
- Email validation with real-time feedback
- Rate limiting display (e.g., "Try again in 5 minutes")
- Success state with instructions
- Link back to login page

### 3.2 PasswordResetForm Component
**File**: `frontend/src/components/auth/PasswordResetForm.vue`

#### Props:
```typescript
interface PasswordResetFormProps {
  token: string;
  loading?: boolean;
  onSuccess?: () => void;
}
```

#### Features:
- Password strength meter
- Real-time validation feedback
- Password confirmation matching
- Security requirements checklist
- Token validation handling

## 4. Security Management Components

### 4.1 SessionManager Component
**File**: `frontend/src/components/security/SessionManager.vue`

#### Props:
```typescript
interface SessionManagerProps {
  sessions: UserSession[];
  currentSessionId: string;
  onTerminate?: (sessionId: string) => void;
  onTerminateAll?: () => void;
}

interface UserSession {
  id: string;
  deviceInfo: string;
  ipAddress: string;
  location?: string;
  startTime: Date;
  lastActivity: Date;
  isCurrent: boolean;
}
```

#### Features:
- Session list with device/location info
- Individual session termination
- Bulk termination (all other sessions)
- Real-time session status updates
- Security warnings for suspicious sessions

### 4.2 SecurityNotifications Component
**File**: `frontend/src/components/security/SecurityNotifications.vue`

#### Props:
```typescript
interface SecurityNotificationsProps {
  notifications: SecurityNotification[];
  preferences: NotificationPreferences;
  onUpdatePreferences?: (prefs: NotificationPreferences) => void;
}

interface SecurityNotification {
  id: string;
  type: 'login' | 'password_change' | 'mfa_setup' | 'suspicious_activity';
  message: string;
  timestamp: Date;
  isRead: boolean;
  severity: 'info' | 'warning' | 'error';
}
```

## 5. Enhanced Error Handling

### 5.1 AuthError Component
**File**: `frontend/src/components/auth/AuthError.vue`

#### Props:
```typescript
interface AuthErrorProps {
  error: AuthError;
  onRetry?: () => void;
  onDismiss?: () => void;
}

interface AuthError {
  type: 'invalid_credentials' | 'account_locked' | 'mfa_required' | 'token_expired' | 'network_error';
  message: string;
  details?: string;
  retryable: boolean;
  actionText?: string;
}
```

#### Error Types and Messages:
- **invalid_credentials**: "Username or password is incorrect"
- **account_locked**: "Account locked due to multiple failed attempts. Try again in X minutes"
- **mfa_required**: "Multi-factor authentication code required"
- **token_expired**: "Session expired. Please log in again"
- **network_error**: "Connection error. Please check your internet connection"

## 6. API Integration Specifications

### 6.1 Authentication API Service
**File**: `frontend/src/api/auth.ts`

#### Methods:
```typescript
interface AuthApiService {
  login(credentials: LoginCredentials): Promise<LoginResponse>;
  logout(): Promise<void>;
  refreshToken(): Promise<TokenResponse>;
  forgotPassword(email: string): Promise<void>;
  resetPassword(token: string, password: string): Promise<void>;
  setupMfa(): Promise<MfaSetupResponse>;
  verifyMfa(code: string): Promise<void>;
  generateBackupCodes(): Promise<string[]>;
  getSessions(): Promise<UserSession[]>;
  terminateSession(sessionId: string): Promise<void>;
}
```

### 6.2 Response Types:
```typescript
interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: UserInfo;
  mfa_required?: boolean;
}

interface MfaSetupResponse {
  qr_code_url: string;
  manual_entry_key: string;
  backup_codes: string[];
}
```

## 7. State Management Integration

### 7.1 Auth Store Enhancements
**File**: `frontend/src/store/auth.ts`

#### Additional State:
```typescript
interface AuthState {
  // Existing state...
  mfaSetupInProgress: boolean;
  securityNotifications: SecurityNotification[];
  activeSessions: UserSession[];
  lastPasswordChange: Date | null;
  accountLockoutInfo: {
    isLocked: boolean;
    unlockTime: Date | null;
    failedAttempts: number;
  };
}
```

#### Additional Actions:
```typescript
interface AuthActions {
  // Existing actions...
  setupMfa(): Promise<MfaSetupResponse>;
  verifyMfaSetup(code: string): Promise<void>;
  disableMfa(): Promise<void>;
  generateBackupCodes(): Promise<string[]>;
  fetchSessions(): Promise<void>;
  terminateSession(sessionId: string): Promise<void>;
  markNotificationRead(notificationId: string): Promise<void>;
}
```

## 8. Accessibility Requirements

### 8.1 ARIA Labels and Roles:
- All form inputs must have proper labels
- Error messages must be associated with inputs
- Loading states must be announced to screen readers
- Focus management for modal dialogs and wizards

### 8.2 Keyboard Navigation:
- Tab order must be logical and complete
- Enter key should submit forms
- Escape key should close modals
- Arrow keys for navigation in multi-step wizards

### 8.3 Color and Contrast:
- Error states must not rely solely on color
- Minimum contrast ratio of 4.5:1 for text
- Focus indicators must be clearly visible

## 9. Testing Requirements

### 9.1 Unit Tests:
- Component rendering with different props
- Form validation logic
- Error state handling
- Event emission verification

### 9.2 Integration Tests:
- Complete authentication flows
- MFA setup and verification
- Password reset process
- Session management operations

### 9.3 E2E Tests:
- Full user journeys from login to dashboard
- Error scenarios and recovery
- Cross-browser compatibility
- Mobile responsiveness

## 10. Performance Considerations

### 10.1 Code Splitting:
- Lazy load MFA setup components
- Separate bundle for password reset flow
- Dynamic imports for security management

### 10.2 Caching:
- Cache user session data
- Optimize QR code generation
- Minimize API calls for session updates

### 10.3 Bundle Size:
- Tree-shake unused authentication libraries
- Optimize QR code library imports
- Minimize CSS bundle size
