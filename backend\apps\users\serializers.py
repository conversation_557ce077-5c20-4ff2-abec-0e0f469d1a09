from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from .models import User, Department, Team, Role
import pyotp
import logging


class DepartmentSerializer(serializers.ModelSerializer):
    """部门序列化器"""

    class Meta:
        model = Department
        fields = ["id", "name", "description", "parent", "created_at", "updated_at"]
        read_only_fields = ["id", "created_at", "updated_at"]


class TeamSerializer(serializers.ModelSerializer):
    """团队序列化器"""

    department_name = serializers.CharField(source="department.name", read_only=True)

    class Meta:
        model = Team
        fields = [
            "id",
            "name",
            "description",
            "department",
            "department_name",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class RoleSerializer(serializers.ModelSerializer):
    """角色序列化器"""

    class Meta:
        model = Role
        fields = [
            "id",
            "name",
            "description",
            "permissions",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""

    department_name = serializers.CharField(source="department.name", read_only=True)
    team_name = serializers.CharField(source="team.name", read_only=True)
    role_name = serializers.CharField(source="role.get_name_display", read_only=True)
    homePath = serializers.CharField(source="home_path", read_only=True)

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "name",
            "first_name",
            "last_name",
            "phone",
            "avatar",
            "department",
            "department_name",
            "team",
            "team_name",
            "role",
            "role_name",
            "homePath",
            "is_mfa_enabled",
            "last_password_change",
            "is_active",
            "is_staff",
            "date_joined",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "date_joined",
            "created_at",
            "updated_at",
            "last_password_change",
        ]


class UserCreateSerializer(serializers.ModelSerializer):
    """用户创建序列化器"""

    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "name",
            "password",
            "password_confirm",
            "first_name",
            "last_name",
            "phone",
            "department",
            "team",
            "role",
        ]
        read_only_fields = ["id"]

    def validate(self, attrs):
        if attrs["password"] != attrs["password_confirm"]:
            raise serializers.ValidationError(_("两次输入的密码不一致"))
        return attrs

    def create(self, validated_data):
        validated_data.pop("password_confirm")
        password = validated_data.pop("password")
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.last_password_change = timezone.now()
        user.save()
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户更新序列化器"""

    class Meta:
        model = User
        fields = [
            "name",
            "first_name",
            "last_name",
            "phone",
            "avatar",
            "department",
            "team",
            "role",
        ]


logger = logging.getLogger(__name__)


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""

    username = serializers.CharField(required=True)
    password = serializers.CharField(write_only=True)
    mfa_code = serializers.CharField(max_length=6, required=False, allow_blank=True)

    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")
        mfa_code = attrs.get("mfa_code")

        logger.debug(f"开始验证用户登录: username={username}")

        if password:
            # 只使用用户名进行认证
            user = authenticate(
                # request=self.context.get('request'),
                username=username,
                password=password,
            )

            if not user:
                logger.warning(f"用户认证失败: username={username}")
                # 尝试增加失败登录次数
                try:
                    user_obj = User.objects.get(username=username)
                    user_obj.increment_failed_attempts()
                    logger.info(f"增加失败登录次数: username={username}")
                except User.DoesNotExist:
                    logger.warning(f"用户不存在: username={username}")
                    pass
                raise serializers.ValidationError(_("用户名或密码错误"))

            if not user.is_active:
                logger.warning(f"禁用用户尝试登录: username={username}")
                raise serializers.ValidationError(_("用户账户已被禁用"))

            if user.is_locked:
                logger.warning(f"锁定用户尝试登录: username={username}")
                raise serializers.ValidationError(_("账户已被锁定，请稍后再试"))

            # 检查MFA
            if user.is_mfa_enabled:
                logger.debug(f"开始MFA验证: username={username}")
                if not mfa_code:
                    logger.warning(f"未提供MFA验证码: username={username}")
                    raise serializers.ValidationError(_("请输入多因素认证码"))

                totp = pyotp.TOTP(user.mfa_secret)
                if not totp.verify(mfa_code):
                    logger.warning(f"MFA验证失败: username={username}")
                    user.increment_failed_attempts()
                    raise serializers.ValidationError(_("多因素认证码错误"))
                logger.debug(f"MFA验证成功: username={username}")

            # 登录成功，重置失败次数
            user.reset_failed_attempts()
            logger.info(f"用户登录成功: username={username}")
            attrs["user"] = user
            return attrs
        else:
            logger.warning("登录请求未提供密码")
            raise serializers.ValidationError(_("必须提供密码"))


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""

    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(
        write_only=True, validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(write_only=True)

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError(_("原密码错误"))
        return value

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError(_("两次输入的新密码不一致"))
        return attrs

    def save(self):
        user = self.context["request"].user
        user.set_password(self.validated_data["new_password"])
        user.last_password_change = timezone.now()
        user.save()
        return user


class PasswordResetSerializer(serializers.Serializer):
    """密码重置序列化器"""

    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            user = User.objects.get(email=value, is_active=True)
            self.user = user
        except User.DoesNotExist:
            raise serializers.ValidationError(_("该邮箱未注册或用户已被禁用"))
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """密码重置确认序列化器"""

    token = serializers.CharField()
    new_password = serializers.CharField(
        write_only=True, validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError(_("两次输入的密码不一致"))
        return attrs


class MFASetupSerializer(serializers.Serializer):
    """MFA设置序列化器"""

    enable = serializers.BooleanField()
    mfa_code = serializers.CharField(max_length=6, required=False)

    def validate(self, attrs):
        user = self.context["request"].user
        enable = attrs.get("enable")
        mfa_code = attrs.get("mfa_code")

        if enable:
            if not mfa_code:
                raise serializers.ValidationError(_("启用MFA时必须提供验证码"))

            # 生成新的密钥或使用现有密钥
            if not user.mfa_secret:
                user.mfa_secret = pyotp.random_base32()

            totp = pyotp.TOTP(user.mfa_secret)
            if not totp.verify(mfa_code):
                raise serializers.ValidationError(_("验证码错误"))

        return attrs

    def save(self):
        user = self.context["request"].user
        enable = self.validated_data["enable"]

        if enable:
            if not user.mfa_secret:
                user.mfa_secret = pyotp.random_base32()
            user.is_mfa_enabled = True
        else:
            user.is_mfa_enabled = False
            user.mfa_secret = ""

        user.save()
        return user


class MFAQRCodeSerializer(serializers.Serializer):
    """MFA二维码序列化器"""

    def to_representation(self, instance):
        user = self.context["request"].user

        # 如果用户没有MFA密钥，生成一个临时的
        if not user.mfa_secret:
            secret = pyotp.random_base32()
        else:
            secret = user.mfa_secret

        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            name=user.email, issuer_name="Password Locker"
        )

        return {"secret": secret, "qr_code_url": provisioning_uri}
