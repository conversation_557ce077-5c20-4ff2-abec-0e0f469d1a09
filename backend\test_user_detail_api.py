#!/usr/bin/env python
"""
测试用户详情API的脚本
"""
import requests
import json

def test_user_detail_api():
    """测试用户详情API"""
    base_url = "http://localhost:8001/api/users"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试用户详情API ===")
    
    # 获取用户列表，找到第一个用户
    response = requests.get(f"{base_url}/users/", headers=headers)
    if response.status_code == 200:
        users = response.json()
        if users.get('results'):
            first_user = users['results'][0]
            user_id = first_user['id']
            print(f"测试用户ID: {user_id}")
            print(f"用户列表中的用户信息:")
            print(json.dumps(first_user, indent=2, ensure_ascii=False))
            
            # 获取用户详情
            print(f"\n获取用户详情:")
            response = requests.get(f"{base_url}/users/{user_id}/", headers=headers)
            if response.status_code == 200:
                user_detail = response.json()
                print(f"用户详情信息:")
                print(json.dumps(user_detail, indent=2, ensure_ascii=False))
            else:
                print(f"获取用户详情失败: {response.text}")
        else:
            print("没有找到用户")
    else:
        print(f"获取用户列表失败: {response.text}")

if __name__ == "__main__":
    test_user_detail_api()
