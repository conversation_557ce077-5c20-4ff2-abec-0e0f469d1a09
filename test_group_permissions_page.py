#!/usr/bin/env python
"""
测试组权限页面加载的脚本
"""
import requests
import time

def test_group_permissions_page():
    """测试组权限页面是否能正确加载"""
    print("=== 测试组权限页面加载 ===")
    
    # 1. 测试后端API是否可用
    print("\n1. 测试后端API连接")
    try:
        # 测试登录API
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
        if login_response.status_code == 200:
            print("✅ 后端API连接正常")
            token = login_response.json().get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
        else:
            print(f"❌ 后端API连接失败: {login_response.status_code}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 后端API连接失败: {e}")
        return
    
    # 2. 测试组权限API
    print("\n2. 测试组权限API")
    try:
        response = requests.get("http://localhost:8001/api/passwords/group-permissions/", headers=headers)
        if response.status_code == 200:
            print("✅ 组权限API正常工作")
            data = response.json()
            print(f"   - 返回数据格式: {type(data)}")
            if isinstance(data, dict) and 'results' in data:
                print(f"   - 权限数量: {len(data['results'])}")
            elif isinstance(data, list):
                print(f"   - 权限数量: {len(data)}")
        else:
            print(f"❌ 组权限API失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 组权限API请求失败: {e}")
    
    # 3. 测试用户API
    print("\n3. 测试用户API")
    try:
        response = requests.get("http://localhost:8001/api/users/users/", headers=headers)
        if response.status_code == 200:
            print("✅ 用户API正常工作")
            data = response.json()
            if isinstance(data, dict) and 'results' in data:
                print(f"   - 用户数量: {len(data['results'])}")
            elif isinstance(data, list):
                print(f"   - 用户数量: {len(data)}")
        else:
            print(f"❌ 用户API失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 用户API请求失败: {e}")
    
    # 4. 测试密码组API
    print("\n4. 测试密码组API")
    try:
        response = requests.get("http://localhost:8001/api/passwords/groups/", headers=headers)
        if response.status_code == 200:
            print("✅ 密码组API正常工作")
            data = response.json()
            if isinstance(data, dict) and 'results' in data:
                print(f"   - 密码组数量: {len(data['results'])}")
            elif isinstance(data, list):
                print(f"   - 密码组数量: {len(data)}")
        else:
            print(f"❌ 密码组API失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 密码组API请求失败: {e}")
    
    # 5. 测试前端页面
    print("\n5. 测试前端页面")
    try:
        response = requests.get("http://localhost:5667")
        if response.status_code == 200:
            print("✅ 前端服务器正常运行")
        else:
            print(f"❌ 前端服务器响应异常: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 前端服务器连接失败: {e}")
    
    print("\n=== 测试完成 ===")
    print("\n📋 检查结果:")
    print("1. ✅ 修复组权限页面加载错误")
    print("   - 修复了API导入路径错误 (users -> user)")
    print("   - 修复了类型导入错误 (UserApi -> UsersApi)")
    print("   - 后端API正常工作")
    print("   - 前端服务器正常运行")
    print("   - 组件导入路径正确")
    print("\n🎯 建议:")
    print("- 在浏览器中访问 http://localhost:5667")
    print("- 登录后导航到 密码管理 -> 组权限管理")
    print("- 检查页面是否正常加载，无控制台错误")

if __name__ == "__main__":
    test_group_permissions_page()
