# Generated by Django 5.2.4 on 2025-07-28 13:18

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BackupRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='备份名称')),
                ('description', models.TextField(blank=True, verbose_name='备份描述')),
                ('backup_type', models.CharField(choices=[('full', '完整备份'), ('incremental', '增量备份'), ('differential', '差异备份'), ('manual', '手动备份')], default='manual', max_length=15, verbose_name='备份类型')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=10, verbose_name='备份状态')),
                ('include_passwords', models.BooleanField(default=True, verbose_name='包含密码')),
                ('include_categories', models.BooleanField(default=True, verbose_name='包含分类')),
                ('include_users', models.BooleanField(default=True, verbose_name='包含用户')),
                ('include_settings', models.BooleanField(default=True, verbose_name='包含设置')),
                ('include_logs', models.BooleanField(default=False, verbose_name='包含日志')),
                ('include_attachments', models.BooleanField(default=True, verbose_name='包含附件')),
                ('storage_type', models.CharField(choices=[('local', '本地存储'), ('s3', 'Amazon S3'), ('azure', 'Azure Blob'), ('gcs', 'Google Cloud Storage'), ('ftp', 'FTP服务器'), ('sftp', 'SFTP服务器')], default='local', max_length=10, verbose_name='存储类型')),
                ('storage_path', models.CharField(max_length=500, verbose_name='存储路径')),
                ('file_name', models.CharField(blank=True, max_length=255, verbose_name='文件名')),
                ('file_size', models.BigIntegerField(blank=True, null=True, verbose_name='文件大小')),
                ('is_encrypted', models.BooleanField(default=True, verbose_name='是否加密')),
                ('encryption_method', models.CharField(blank=True, max_length=50, verbose_name='加密方法')),
                ('is_compressed', models.BooleanField(default=True, verbose_name='是否压缩')),
                ('compression_method', models.CharField(blank=True, max_length=20, verbose_name='压缩方法')),
                ('compression_ratio', models.FloatField(blank=True, null=True, verbose_name='压缩比')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('total_records', models.IntegerField(blank=True, null=True, verbose_name='总记录数')),
                ('processed_records', models.IntegerField(default=0, verbose_name='已处理记录数')),
                ('checksum', models.CharField(blank=True, max_length=128, verbose_name='校验和')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='过期时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '备份记录',
                'verbose_name_plural': '备份记录',
                'db_table': 'backup_records',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NotificationRecipient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_read', models.BooleanField(default=False, verbose_name='已读')),
                ('is_dismissed', models.BooleanField(default=False, verbose_name='已消除')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='阅读时间')),
                ('dismissed_at', models.DateTimeField(blank=True, null=True, verbose_name='消除时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '通知接收者',
                'verbose_name_plural': '通知接收者',
                'db_table': 'notification_recipients',
            },
        ),
        migrations.CreateModel(
            name='RestoreRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='恢复名称')),
                ('description', models.TextField(blank=True, verbose_name='恢复描述')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=10, verbose_name='恢复状态')),
                ('restore_mode', models.CharField(choices=[('full', '完整恢复'), ('selective', '选择性恢复'), ('merge', '合并恢复')], default='full', max_length=10, verbose_name='恢复模式')),
                ('restore_passwords', models.BooleanField(default=True, verbose_name='恢复密码')),
                ('restore_categories', models.BooleanField(default=True, verbose_name='恢复分类')),
                ('restore_users', models.BooleanField(default=False, verbose_name='恢复用户')),
                ('restore_settings', models.BooleanField(default=False, verbose_name='恢复设置')),
                ('restore_attachments', models.BooleanField(default=True, verbose_name='恢复附件')),
                ('overwrite_existing', models.BooleanField(default=False, verbose_name='覆盖现有数据')),
                ('skip_duplicates', models.BooleanField(default=True, verbose_name='跳过重复项')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('total_records', models.IntegerField(blank=True, null=True, verbose_name='总记录数')),
                ('processed_records', models.IntegerField(default=0, verbose_name='已处理记录数')),
                ('created_records', models.IntegerField(default=0, verbose_name='新建记录数')),
                ('updated_records', models.IntegerField(default=0, verbose_name='更新记录数')),
                ('skipped_records', models.IntegerField(default=0, verbose_name='跳过记录数')),
                ('failed_records', models.IntegerField(default=0, verbose_name='失败记录数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('backup_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='restore_records', to='system.backuprecord', verbose_name='备份记录')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '恢复记录',
                'verbose_name_plural': '恢复记录',
                'db_table': 'restore_records',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemNotification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='通知标题')),
                ('message', models.TextField(verbose_name='通知内容')),
                ('notification_type', models.CharField(choices=[('info', '信息'), ('warning', '警告'), ('error', '错误'), ('success', '成功')], default='info', max_length=10, verbose_name='通知类型')),
                ('priority', models.CharField(choices=[('low', '低'), ('normal', '普通'), ('high', '高'), ('urgent', '紧急')], default='normal', max_length=10, verbose_name='优先级')),
                ('is_global', models.BooleanField(default=False, verbose_name='全局通知')),
                ('is_persistent', models.BooleanField(default=False, verbose_name='持久通知')),
                ('auto_dismiss', models.BooleanField(default=True, verbose_name='自动消失')),
                ('dismiss_after', models.IntegerField(blank=True, null=True, verbose_name='消失时间(秒)')),
                ('action_url', models.URLField(blank=True, verbose_name='操作链接')),
                ('action_text', models.CharField(blank=True, max_length=50, verbose_name='操作文本')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='过期时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_notifications', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('target_users', models.ManyToManyField(related_name='notifications', through='system.NotificationRecipient', to=settings.AUTH_USER_MODEL, verbose_name='目标用户')),
            ],
            options={
                'verbose_name': '系统通知',
                'verbose_name_plural': '系统通知',
                'db_table': 'system_notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='notificationrecipient',
            name='notification',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='system.systemnotification', verbose_name='通知'),
        ),
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='设置键')),
                ('name', models.CharField(max_length=200, verbose_name='设置名称')),
                ('description', models.TextField(blank=True, verbose_name='设置描述')),
                ('category', models.CharField(choices=[('security', '安全设置'), ('password', '密码策略'), ('notification', '通知设置'), ('backup', '备份设置'), ('ui', '界面设置'), ('integration', '集成设置'), ('audit', '审计设置'), ('system', '系统设置')], default='system', max_length=20, verbose_name='设置分类')),
                ('value_type', models.CharField(choices=[('string', '字符串'), ('integer', '整数'), ('float', '浮点数'), ('boolean', '布尔值'), ('json', 'JSON'), ('text', '文本')], default='string', max_length=10, verbose_name='值类型')),
                ('value', models.TextField(verbose_name='设置值')),
                ('default_value', models.TextField(verbose_name='默认值')),
                ('validation_rules', models.JSONField(blank=True, default=dict, help_text='JSON格式的验证规则', verbose_name='验证规则')),
                ('is_public', models.BooleanField(default=False, verbose_name='公开设置')),
                ('is_readonly', models.BooleanField(default=False, verbose_name='只读设置')),
                ('requires_restart', models.BooleanField(default=False, verbose_name='需要重启')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('group', models.CharField(blank=True, max_length=50, verbose_name='分组')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='修改者')),
            ],
            options={
                'verbose_name': '系统设置',
                'verbose_name_plural': '系统设置',
                'db_table': 'system_settings',
                'ordering': ['category', 'order', 'name'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='notificationrecipient',
            unique_together={('notification', 'user')},
        ),
    ]
