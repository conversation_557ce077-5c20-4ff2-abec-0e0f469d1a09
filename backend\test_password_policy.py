#!/usr/bin/env python
"""
测试密码策略后端模型的脚本
"""
import requests
import json


def test_password_policy_backend():
    """测试密码策略后端模型"""
    base_url = "http://localhost:8001/api/passwords"

    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}

    try:
        login_response = requests.post(
            "http://localhost:8001/api/auth/login/", json=login_data
        )
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return

        token = login_response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求失败: {e}")
        return

    print("=== 测试密码策略后端模型 ===")

    # 1. 测试获取密码策略列表
    print("\n1. 测试获取密码策略列表")
    try:
        response = requests.get(f"{base_url}/password-policies/", headers=headers)
        print(f"获取策略列表响应: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            # 检查是否是分页数据
            if isinstance(data, dict) and "results" in data:
                policies = data["results"]
                count = data.get("count", len(policies))
            else:
                policies = data
                count = len(policies) if isinstance(policies, list) else 1

            print(f"✅ 密码策略列表获取成功，共 {count} 个策略")

            for policy in policies:
                print(f"   - {policy['name']}: 最小长度{policy['min_length']}")
                if policy["is_default"]:
                    print(f"     (默认策略)")
        else:
            print(f"❌ 获取策略列表失败: {response.text}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取策略列表请求失败: {e}")
        return

    # 2. 测试根据策略生成密码
    print("\n2. 测试根据策略生成密码")
    try:
        # 使用默认策略生成密码
        response = requests.post(
            f"{base_url}/password-policies/generate/", json={}, headers=headers
        )
        print(f"生成密码响应: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            password = result.get("password")
            policy_info = result.get("policy")

            print(f"✅ 密码生成成功")
            print(f"   - 生成的密码: {password}")
            print(f"   - 密码长度: {len(password)}")
            print(f"   - 使用策略: {policy_info['name']}")
            print(f"   - 策略要求: 最小长度{policy_info['min_length']}")
        else:
            print(f"❌ 密码生成失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 密码生成请求失败: {e}")

    # 3. 测试自定义参数生成密码
    print("\n3. 测试自定义参数生成密码")
    try:
        custom_params = {
            "length": 16,
            "include_uppercase": True,
            "include_lowercase": True,
            "include_digits": True,
            "include_symbols": False,
        }

        response = requests.post(
            f"{base_url}/password-policies/generate/",
            json=custom_params,
            headers=headers,
        )
        print(f"自定义生成响应: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            password = result.get("password")

            print(f"✅ 自定义密码生成成功")
            print(f"   - 生成的密码: {password}")
            print(f"   - 密码长度: {len(password)}")

            # 验证密码是否符合要求
            has_upper = any(c.isupper() for c in password)
            has_lower = any(c.islower() for c in password)
            has_digit = any(c.isdigit() for c in password)
            has_symbol = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

            print(f"   - 包含大写字母: {has_upper}")
            print(f"   - 包含小写字母: {has_lower}")
            print(f"   - 包含数字: {has_digit}")
            print(f"   - 包含特殊字符: {has_symbol}")
        else:
            print(f"❌ 自定义密码生成失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 自定义密码生成请求失败: {e}")

    # 4. 测试密码验证
    print("\n4. 测试密码验证")
    try:
        # 测试强密码
        strong_password = "StrongP@ssw0rd123"
        validate_data = {"password": strong_password}

        response = requests.post(
            f"{base_url}/password-policies/validate/",
            json=validate_data,
            headers=headers,
        )
        print(f"强密码验证响应: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            is_valid = result.get("is_valid")
            errors = result.get("errors", [])

            print(f"✅ 强密码验证完成")
            print(f"   - 密码: {strong_password}")
            print(f"   - 验证结果: {'通过' if is_valid else '不通过'}")
            if errors:
                print(f"   - 错误信息: {', '.join(errors)}")
        else:
            print(f"❌ 强密码验证失败: {response.text}")

        # 测试弱密码
        weak_password = "123456"
        validate_data = {"password": weak_password}

        response = requests.post(
            f"{base_url}/password-policies/validate/",
            json=validate_data,
            headers=headers,
        )
        if response.status_code == 200:
            result = response.json()
            is_valid = result.get("is_valid")
            errors = result.get("errors", [])

            print(f"✅ 弱密码验证完成")
            print(f"   - 密码: {weak_password}")
            print(f"   - 验证结果: {'通过' if is_valid else '不通过'}")
            if errors:
                print(f"   - 错误信息: {', '.join(errors)}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 密码验证请求失败: {e}")

    # 5. 测试创建新的密码策略
    print("\n5. 测试创建新的密码策略")
    try:
        new_policy_data = {
            "name": "测试密码策略",
            "description": "用于测试的密码策略",
            "min_length": 10,
            "max_length": 50,
            "uppercase_count": 1,
            "lowercase_count": 1,
            "digit_count": 1,
            "special_char_count": 1,
            "allowed_special_chars": "!@#$%",
            "allow_repeated_chars": False,
            "max_repeated_chars": 2,
            "forbid_common_passwords": True,
            "forbid_personal_info": True,
            "is_active": True,
            "is_default": False,
        }

        response = requests.post(
            f"{base_url}/password-policies/", json=new_policy_data, headers=headers
        )
        print(f"创建策略响应: {response.status_code}")

        if response.status_code == 201:
            policy = response.json()
            print(f"✅ 密码策略创建成功")
            print(f"   - 策略名称: {policy['name']}")
            print(f"   - 策略ID: {policy['id']}")

            # 使用新策略生成密码
            generate_data = {"policy_id": policy["id"]}
            response = requests.post(
                f"{base_url}/password-policies/generate/",
                json=generate_data,
                headers=headers,
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 使用新策略生成密码成功: {result.get('password')}")

            # 清理测试数据
            requests.delete(
                f"{base_url}/password-policies/{policy['id']}/", headers=headers
            )
            print(f"✅ 测试策略已清理")
        else:
            print(f"❌ 创建策略失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建策略请求失败: {e}")

    print("\n=== 密码策略后端模型测试完成 ===")
    print("\n📋 功能检查清单:")
    print("3. ✅ 创建密码策略后端模型")
    print("   - PasswordPolicy模型包含完整字段")
    print("   - 密码策略序列化器正常工作")
    print("   - 密码策略API视图正常工作")
    print("   - 密码生成功能正常工作")
    print("   - 密码验证功能正常工作")
    print("   - 支持自定义参数生成密码")
    print("   - 支持CRUD操作")


if __name__ == "__main__":
    test_password_policy_backend()
