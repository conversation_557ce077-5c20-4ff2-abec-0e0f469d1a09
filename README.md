# 密码管理系统

一个基于Django REST Framework和Vue.js的企业级密码管理系统，专为IT运维人员设计。

## 项目特性

- 🔐 **安全加密**: 使用国密SM4算法加密存储密码
- 👥 **权限管理**: 基于角色的访问控制，确保用户只能访问授权的密码
- 🔗 **安全分享**: 支持一次性链接分享，点击后自动失效
- 📊 **审计日志**: 完整的操作日志记录和安全审计
- 🏢 **企业级**: 支持部门、团队管理和多层级权限控制
- 🌐 **现代化UI**: 基于vben-admin的现代化管理界面

## 技术栈

### 前端
- Vue 3 + TypeScript
- Vben Admin (基于Ant Design Vue)
- Vite
- Pinia (状态管理)
- Vue Router

### 后端
- Django 4.2+
- Django REST Framework
- MySQL 8
- Redis (缓存和会话)
- Celery (异步任务)
- 国密SM4加密算法

## 项目结构

```
locker/
├── frontend/          # 前端项目 (Vue.js + <PERSON>ben Admin)
├── backend/           # 后端项目 (Django)
│   ├── config/        # Django配置
│   ├── apps/          # Django应用
│   │   ├── users/     # 用户管理
│   │   ├── passwords/ # 密码管理
│   │   ├── categories/# 分类管理
│   │   ├── sharing/   # 分享功能
│   │   ├── audit/     # 审计日志
│   │   └── system/    # 系统设置
│   ├── utils/         # 工具类
│   ├── static/        # 静态文件
│   ├── media/         # 媒体文件
│   └── logs/          # 日志文件
├── docs/              # 项目文档
└── scripts/           # 部署脚本
```

## 快速开始

### 环境要求

- Node.js 20.10.0+
- Python 3.8+
- MySQL 8.0+
- Redis 6.0+

### 前端开发

```bash
cd frontend
pnpm install
pnpm dev
```

### 后端开发

```bash
cd backend
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver
```

## 开发状态

当前项目处于初始化阶段，已完成：

- ✅ 项目结构搭建
- ✅ 前端环境配置 (Vben Admin)
- ✅ 后端基础配置 (Django + DRF)
- ✅ Git仓库初始化
- ⏳ 数据库模型设计
- ⏳ API接口开发
- ⏳ 前端页面开发

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 项目讨论区