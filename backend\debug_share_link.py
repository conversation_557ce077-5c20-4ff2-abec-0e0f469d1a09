#!/usr/bin/env python
"""
调试分享链接状态的脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.sharing.models import OneTimeLink
from django.utils import timezone

def debug_share_link(token):
    """调试指定token的分享链接"""
    try:
        link = OneTimeLink.objects.get(token=token)
        
        print(f"=== 分享链接调试信息 ===")
        print(f"Token: {link.token}")
        print(f"密码条目: {link.password_entry.title}")
        print(f"创建者: {link.created_by.username}")
        print(f"状态: {link.status}")
        print(f"创建时间: {link.created_at}")
        print(f"过期时间: {link.expires_at}")
        print(f"当前时间: {timezone.now()}")
        print(f"是否过期: {link.is_expired}")
        print(f"是否有效: {link.is_valid}")
        print(f"访问时间: {link.accessed_at}")
        print(f"访问IP: {link.accessed_ip}")
        
        # 检查过期原因
        if link.is_expired:
            print(f"\n过期原因: 当前时间 ({timezone.now()}) > 过期时间 ({link.expires_at})")
        
        # 检查无效原因
        if not link.is_valid:
            print(f"\n无效原因:")
            print(f"  - 状态是否为active: {link.status == 'active'}")
            print(f"  - 是否未过期: {not link.is_expired}")
            
    except OneTimeLink.DoesNotExist:
        print(f"找不到token为 {token} 的分享链接")

if __name__ == "__main__":
    # 调试最新的分享链接
    token = "qVPw9nntoPg"
    debug_share_link(token)
    
    print("\n=== 所有活跃的分享链接 ===")
    for link in OneTimeLink.objects.filter(status="active").order_by("-created_at")[:5]:
        print(f"Token: {link.token}, 标题: {link.password_entry.title}, 状态: {link.status}, 过期: {link.is_expired}, 有效: {link.is_valid}")
