#!/usr/bin/env python
"""
调试加密解密问题的脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.passwords.models import PasswordEntry
from utils.encryption import encrypt_data, decrypt_data

def debug_encryption():
    """调试加密解密问题"""
    print("=== 调试加密解密问题 ===")
    
    # 查找最新的密码条目
    latest_password = PasswordEntry.objects.order_by('-created_at').first()
    if not latest_password:
        print("❌ 没有找到密码条目")
        return
    
    print(f"密码条目: {latest_password.title}")
    print(f"存储的加密密码: {latest_password.password}")
    print(f"加密密码长度: {len(latest_password.password)}")
    
    # 尝试解密
    try:
        decrypted = decrypt_data(latest_password.password)
        print(f"解密结果: '{decrypted}'")
        print(f"解密结果长度: {len(decrypted)}")
        print(f"解密结果类型: {type(decrypted)}")
        
        # 检查是否是base64编码
        import base64
        try:
            decoded = base64.b64decode(decrypted)
            print(f"Base64解码结果: {decoded}")
            print(f"Base64解码结果长度: {len(decoded)}")
            try:
                decoded_str = decoded.decode('utf-8')
                print(f"UTF-8解码结果: '{decoded_str}'")
            except:
                print("无法UTF-8解码")
        except:
            print("不是有效的base64数据")
            
    except Exception as e:
        print(f"❌ 解密失败: {e}")
    
    # 测试正确的加密解密流程
    print(f"\n=== 测试正确的加密解密流程 ===")
    test_password = "TestPassword123!"
    print(f"原始密码: '{test_password}'")
    
    encrypted = encrypt_data(test_password)
    print(f"加密结果: {encrypted}")
    print(f"加密结果长度: {len(encrypted)}")
    
    decrypted = decrypt_data(encrypted)
    print(f"解密结果: '{decrypted}'")
    print(f"解密结果长度: {len(decrypted)}")
    
    if test_password == decrypted:
        print("✅ 加密解密正常")
    else:
        print("❌ 加密解密异常")

if __name__ == "__main__":
    debug_encryption()
