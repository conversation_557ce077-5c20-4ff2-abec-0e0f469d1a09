#!/usr/bin/env python
"""
创建测试数据
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordEntry, Category

User = get_user_model()


def create_test_data():
    """创建测试数据"""
    print("开始创建测试数据...")

    # 获取或创建测试用户
    user, created = User.objects.get_or_create(
        username="admin",
        defaults={
            "email": "<EMAIL>",
            "is_staff": True,
            "is_superuser": True,
        },
    )

    if created:
        user.set_password("admin123")
        user.save()
        print(f"创建测试用户: {user.username}")
    else:
        print(f"使用现有用户: {user.username}")

    # 创建分类
    categories_data = [
        {"name": "服务器", "description": "Linux和Windows服务器"},
        {"name": "数据库", "description": "各种数据库系统"},
        {"name": "中间件", "description": "中间件和缓存系统"},
        {"name": "网络设备", "description": "路由器、交换机等网络设备"},
        {"name": "云服务", "description": "云平台和云服务"},
    ]

    categories = []
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_data["name"],
            user=user,
            defaults={"description": cat_data["description"]},
        )
        categories.append(category)
        if created:
            print(f"创建分类: {category.name}")

    # 创建标签
    tags_data = [
        {"name": "生产环境", "color": "#ef4444"},
        {"name": "测试环境", "color": "#3b82f6"},
        {"name": "开发环境", "color": "#10b981"},
        {"name": "重要", "color": "#f59e0b"},
        {"name": "备份", "color": "#8b5cf6"},
    ]

    tags = []
    for tag_data in tags_data:
        tag, created = Tag.objects.get_or_create(
            name=tag_data["name"], user=user, defaults={"color": tag_data["color"]}
        )
        tags.append(tag)
        if created:
            print(f"创建标签: {tag.name}")

    # 创建密码条目
    passwords_data = [
        {
            "title": "生产数据库主库",
            "username": "root",
            "password": "SecurePass123!",
            "hostname": "************0",
            "port": 3306,
            "system_type": "database",
            "database_type": "mysql",
            "environment": "prod",
            "business_system": "用户管理系统",
            "responsible_person": "张三",
            "category": categories[1],  # 数据库
            "notes": "生产环境主数据库，请谨慎操作",
        },
        {
            "title": "Web服务器01",
            "username": "ubuntu",
            "password": "LinuxPass456!",
            "hostname": "************",
            "port": 22,
            "system_type": "linux",
            "protocol": "ssh",
            "environment": "prod",
            "business_system": "官网系统",
            "responsible_person": "李四",
            "category": categories[0],  # 服务器
            "notes": "生产环境Web服务器",
        },
        {
            "title": "Redis缓存集群",
            "username": "redis",
            "password": "RedisPass789!",
            "hostname": "*************",
            "port": 6379,
            "system_type": "middleware",
            "database_type": "redis",
            "environment": "prod",
            "business_system": "缓存系统",
            "responsible_person": "王五",
            "category": categories[2],  # 中间件
            "notes": "Redis缓存集群主节点",
        },
        {
            "title": "测试数据库",
            "username": "testuser",
            "password": "TestPass123!",
            "hostname": "*************",
            "port": 3306,
            "system_type": "database",
            "database_type": "mysql",
            "environment": "test",
            "business_system": "测试系统",
            "responsible_person": "赵六",
            "category": categories[1],  # 数据库
            "notes": "测试环境数据库",
        },
        {
            "title": "开发服务器",
            "username": "developer",
            "password": "DevPass456!",
            "hostname": "************",
            "port": 22,
            "system_type": "linux",
            "protocol": "ssh",
            "environment": "dev",
            "business_system": "开发环境",
            "responsible_person": "孙七",
            "category": categories[0],  # 服务器
            "notes": "开发环境服务器",
        },
    ]

    for pwd_data in passwords_data:
        # 提取分类
        category = pwd_data.pop("category", None)

        # 创建密码条目
        password, created = PasswordEntry.objects.get_or_create(
            title=pwd_data["title"],
            owner=user,
            defaults={
                **pwd_data,
                "category": category,
            },
        )

        if created:
            print(f"创建密码条目: {password.title}")

            # 标签功能已移除，跳过标签设置

    print("测试数据创建完成！")
    print(f"用户数: {User.objects.count()}")
    print(f"分类数: {Category.objects.count()}")
    print(f"密码条目数: {PasswordEntry.objects.count()}")


if __name__ == "__main__":
    create_test_data()
