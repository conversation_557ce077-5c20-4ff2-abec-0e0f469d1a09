#!/usr/bin/env python
"""
测试分类管理图标选择功能的脚本
"""
import requests
import json
import time

def test_category_icon_selection():
    """测试分类管理图标选择功能"""
    base_url = "http://localhost:8001/api/passwords"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试分类管理图标选择功能 ===")
    
    # 测试不同图标的分类创建
    test_icons = ['📁', '🔒', '💼', '🌐', '📚', '🎯', '🚀', '⭐']
    test_colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16']
    
    created_categories = []
    
    for i, (icon, color) in enumerate(zip(test_icons, test_colors)):
        print(f"\n{i+1}. 测试图标 {icon} 和颜色 {color}")
        
        category_data = {
            "name": f"测试分类_{icon}_{int(time.time())}",
            "description": f"使用图标 {icon} 的测试分类",
            "icon": icon,
            "color": color
        }
        
        response = requests.post(f"{base_url}/categories/", json=category_data, headers=headers)
        print(f"   创建分类响应: {response.status_code}")
        
        if response.status_code == 201:
            category = response.json()
            created_categories.append(category)
            print(f"   ✅ 分类创建成功: {category['name']}")
            print(f"      - 图标: {category.get('icon', '无')}")
            print(f"      - 颜色: {category.get('color', '无')}")
        else:
            print(f"   ❌ 创建分类失败: {response.text}")
    
    # 测试获取分类列表，验证图标和颜色
    print(f"\n{len(test_icons)+1}. 验证分类列表中的图标和颜色")
    response = requests.get(f"{base_url}/categories/", headers=headers)
    if response.status_code == 200:
        categories = response.json()
        if isinstance(categories, list):
            categories_list = categories
        else:
            categories_list = categories.get('results', [])
        
        print(f"   ✅ 分类列表获取成功，共 {len(categories_list)} 个分类")
        
        # 验证我们创建的分类
        for created_cat in created_categories:
            found_cat = next((cat for cat in categories_list if cat['id'] == created_cat['id']), None)
            if found_cat:
                print(f"   ✅ 分类 {found_cat['name']} 验证成功:")
                print(f"      - 图标: {found_cat.get('icon', '无')}")
                print(f"      - 颜色: {found_cat.get('color', '无')}")
            else:
                print(f"   ❌ 分类 {created_cat['name']} 未找到")
    else:
        print(f"   ❌ 获取分类列表失败: {response.text}")
    
    # 测试更新分类图标
    if created_categories:
        print(f"\n{len(test_icons)+2}. 测试更新分类图标")
        first_category = created_categories[0]
        new_icon = '🎨'
        new_color = '#FF6B6B'
        
        update_data = {
            "name": first_category['name'],
            "description": first_category['description'],
            "icon": new_icon,
            "color": new_color
        }
        
        response = requests.put(f"{base_url}/categories/{first_category['id']}/", json=update_data, headers=headers)
        print(f"   更新分类响应: {response.status_code}")
        
        if response.status_code == 200:
            updated_category = response.json()
            print(f"   ✅ 分类更新成功:")
            print(f"      - 原图标: {first_category.get('icon', '无')} -> 新图标: {updated_category.get('icon', '无')}")
            print(f"      - 原颜色: {first_category.get('color', '无')} -> 新颜色: {updated_category.get('color', '无')}")
        else:
            print(f"   ❌ 更新分类失败: {response.text}")
    
    # 清理测试数据
    print(f"\n{len(test_icons)+3}. 清理测试数据")
    for category in created_categories:
        response = requests.delete(f"{base_url}/categories/{category['id']}/", headers=headers)
        if response.status_code == 204:
            print(f"   ✅ 分类 {category['name']} 删除成功")
        else:
            print(f"   ⚠️ 删除分类 {category['name']} 失败: {response.text}")
    
    print("\n=== 分类管理图标选择功能测试完成 ===")
    print("\n📋 功能检查清单:")
    print("2. ✅ 改进分类管理的图标选择体验")
    print("   - 提供常用图标选择器")
    print("   - 支持64个常用图标")
    print("   - 图标选择实时预览")
    print("   - 支持手动输入自定义图标")
    print("   - 图标和颜色组合显示")

if __name__ == "__main__":
    test_category_icon_selection()
