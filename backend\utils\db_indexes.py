"""数据库索引优化配置"""

from django.db import models
from django.contrib.postgres.indexes import GinIndex, BTreeIndex


class DatabaseIndexes:
    """数据库索引配置类"""
    
    @staticmethod
    def get_password_indexes():
        """密码相关表的索引配置"""
        return [
            # PasswordEntry表索引
            models.Index(fields=['owner', 'created_at'], name='pwd_owner_created_idx'),
            models.Index(fields=['category', 'is_deleted'], name='pwd_category_deleted_idx'),
            models.Index(fields=['title'], name='pwd_title_idx'),
            models.Index(fields=['url'], name='pwd_url_idx'),
            models.Index(fields=['is_favorite', 'owner'], name='pwd_favorite_owner_idx'),
            models.Index(fields=['updated_at'], name='pwd_updated_idx'),
            
            # Category表索引
            models.Index(fields=['owner', 'name'], name='cat_owner_name_idx'),
            models.Index(fields=['parent', 'is_deleted'], name='cat_parent_deleted_idx'),
            
            # Tag表索引
            models.Index(fields=['name'], name='tag_name_idx'),
            models.Index(fields=['created_by'], name='tag_created_by_idx'),
            
            # PasswordHistory表索引
            models.Index(fields=['password_entry', 'created_at'], name='pwd_hist_entry_created_idx'),
        ]
    
    @staticmethod
    def get_sharing_indexes():
        """分享相关表的索引配置"""
        return [
            # ShareRecord表索引
            models.Index(fields=['shared_by', 'created_at'], name='share_by_created_idx'),
            models.Index(fields=['shared_with', 'status'], name='share_with_status_idx'),
            models.Index(fields=['password_entry', 'status'], name='share_entry_status_idx'),
            models.Index(fields=['expires_at'], name='share_expires_idx'),
            
            # OneTimeLink表索引
            models.Index(fields=['token'], name='otl_token_idx'),
            models.Index(fields=['created_by', 'created_at'], name='otl_created_by_at_idx'),
            models.Index(fields=['expires_at', 'is_used'], name='otl_expires_used_idx'),
            
            # ShareAccessLog表索引
            models.Index(fields=['share_record', 'accessed_at'], name='share_log_record_at_idx'),
            models.Index(fields=['accessed_by', 'accessed_at'], name='share_log_by_at_idx'),
        ]
    
    @staticmethod
    def get_audit_indexes():
        """审计相关表的索引配置"""
        return [
            # OperationLog表索引
            models.Index(fields=['user', 'timestamp'], name='op_log_user_time_idx'),
            models.Index(fields=['action', 'timestamp'], name='op_log_action_time_idx'),
            models.Index(fields=['object_type', 'object_id'], name='op_log_object_idx'),
            models.Index(fields=['ip_address', 'timestamp'], name='op_log_ip_time_idx'),
            
            # LoginLog表索引
            models.Index(fields=['user', 'login_time'], name='login_log_user_time_idx'),
            models.Index(fields=['ip_address', 'login_time'], name='login_log_ip_time_idx'),
            models.Index(fields=['is_successful', 'login_time'], name='login_log_success_time_idx'),
            
            # PasswordAccessLog表索引
            models.Index(fields=['password_entry', 'accessed_at'], name='pwd_access_entry_at_idx'),
            models.Index(fields=['accessed_by', 'accessed_at'], name='pwd_access_by_at_idx'),
            models.Index(fields=['access_type', 'accessed_at'], name='pwd_access_type_at_idx'),
            
            # SecurityEvent表索引
            models.Index(fields=['event_type', 'timestamp'], name='sec_event_type_time_idx'),
            models.Index(fields=['user', 'timestamp'], name='sec_event_user_time_idx'),
            models.Index(fields=['severity', 'timestamp'], name='sec_event_severity_time_idx'),
        ]
    
    @staticmethod
    def get_system_indexes():
        """系统相关表的索引配置"""
        return [
            # SystemSetting表索引
            models.Index(fields=['key'], name='sys_setting_key_idx'),
            models.Index(fields=['category'], name='sys_setting_category_idx'),
            
            # BackupRecord表索引
            models.Index(fields=['backup_type', 'created_at'], name='backup_type_created_idx'),
            models.Index(fields=['status', 'created_at'], name='backup_status_created_idx'),
            
            # SystemNotification表索引
            models.Index(fields=['notification_type', 'created_at'], name='notif_type_created_idx'),
            models.Index(fields=['is_read', 'created_at'], name='notif_read_created_idx'),
        ]
    
    @staticmethod
    def get_user_indexes():
        """用户相关表的索引配置"""
        return [
            # User表索引
            models.Index(fields=['email'], name='user_email_idx'),
            models.Index(fields=['phone'], name='user_phone_idx'),
            models.Index(fields=['department', 'is_active'], name='user_dept_active_idx'),
            models.Index(fields=['team', 'is_active'], name='user_team_active_idx'),
            models.Index(fields=['last_login'], name='user_last_login_idx'),
            models.Index(fields=['date_joined'], name='user_joined_idx'),
            
            # Department表索引
            models.Index(fields=['name'], name='dept_name_idx'),
            models.Index(fields=['parent'], name='dept_parent_idx'),
            
            # Team表索引
            models.Index(fields=['name', 'department'], name='team_name_dept_idx'),
            
            # Role表索引
            models.Index(fields=['name'], name='role_name_idx'),
        ]


def create_custom_indexes():
    """创建自定义索引的函数"""
    from django.db import connection
    
    with connection.cursor() as cursor:
        # 创建复合索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS pwd_search_idx 
            ON passwords_passwordentry(title, username, url, notes)
        """)
        
        # 创建全文搜索索引（如果使用MySQL 5.7+）
        cursor.execute("""
            CREATE FULLTEXT INDEX IF NOT EXISTS pwd_fulltext_idx 
            ON passwords_passwordentry(title, username, url, notes)
        """)
        
        # 创建时间范围查询索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS audit_time_range_idx 
            ON audit_operationlog(timestamp, user_id)
        """)


def drop_custom_indexes():
    """删除自定义索引的函数"""
    from django.db import connection
    
    with connection.cursor() as cursor:
        cursor.execute("DROP INDEX IF EXISTS pwd_search_idx")
        cursor.execute("DROP INDEX IF EXISTS pwd_fulltext_idx")
        cursor.execute("DROP INDEX IF EXISTS audit_time_range_idx")