# Generated by Django 5.2.4 on 2025-08-01 05:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0011_add_soft_delete_fields"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="passwordentry",
            name="protocol",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ssh", "SSH"),
                    ("tcp", "TCP"),
                    ("http", "HTTP"),
                    ("https", "HTTPS"),
                    ("ftp", "FTP"),
                    ("sftp", "SFTP"),
                    ("rdp", "RDP"),
                    ("other", "其他"),
                ],
                max_length=10,
                verbose_name="连接协议",
            ),
        ),
    ]
