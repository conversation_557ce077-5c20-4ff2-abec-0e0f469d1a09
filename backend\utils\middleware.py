import logging
import time
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()
logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(MiddlewareMixin):
    """
    请求日志中间件
    """
    
    def process_request(self, request):
        request.start_time = time.time()
        
        # 记录请求信息
        user = getattr(request, 'user', None)
        user_info = 'Anonymous'
        if user and user.is_authenticated:
            user_info = f'{user.username} (ID: {user.id})'
        
        logger.info(
            f'Request started - Method: {request.method}, '
            f'Path: {request.path}, User: {user_info}, '
            f'IP: {self.get_client_ip(request)}'
        )
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            logger.info(
                f'Request completed - Status: {response.status_code}, '
                f'Duration: {duration:.3f}s'
            )
        
        return response
    
    def process_exception(self, request, exception):
        logger.error(
            f'Request failed - Exception: {type(exception).__name__}: {str(exception)}'
        )
        return None
    
    @staticmethod
    def get_client_ip(request):
        """
        获取客户端IP地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SecurityMiddleware(MiddlewareMixin):
    """
    安全中间件
    """
    
    def process_request(self, request):
        # 检查用户是否被锁定
        if request.user.is_authenticated:
            if hasattr(request.user, 'locked_until') and request.user.locked_until:
                if timezone.now() < request.user.locked_until:
                    from django.http import JsonResponse
                    return JsonResponse({
                        'error': '账户已被锁定，请稍后再试',
                        'locked_until': request.user.locked_until.isoformat()
                    }, status=423)
        
        return None