from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class AuditConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.audit'
    verbose_name = _("审计日志")
    
    def ready(self):
        """应用准备就绪时的初始化操作"""
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
        
        # 注册审计中间件
        try:
            from .middleware import register_audit_middleware
            register_audit_middleware()
        except ImportError:
            pass
        
        # 启动日志清理定时任务
        try:
            from .tasks import start_log_cleanup_scheduler
            start_log_cleanup_scheduler()
        except ImportError:
            pass