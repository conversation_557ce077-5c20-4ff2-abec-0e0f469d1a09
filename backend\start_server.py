#!/usr/bin/env python
"""
开发服务器启动脚本
用于启动密码管理系统的开发服务器
"""

import os
import sys
import subprocess
import django
from django.core.management import execute_from_command_line

def check_requirements():
    """检查依赖是否安装"""
    try:
        import django
        import rest_framework
        import corsheaders
        import cryptography
        import pyotp
        import qrcode
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def setup_database():
    """设置数据库"""
    print("\n设置数据库...")
    
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()
    
    try:
        # 创建迁移文件
        print("创建迁移文件...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # 应用迁移
        print("应用数据库迁移...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✓ 数据库设置完成")
        return True
    except Exception as e:
        print(f"✗ 数据库设置失败: {e}")
        return False

def create_superuser():
    """创建超级用户"""
    print("\n检查超级用户...")
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        if User.objects.filter(is_superuser=True).exists():
            print("✓ 超级用户已存在")
            return True
        
        print("创建超级用户...")
        # 使用预设的超级用户信息
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='Admin',
            last_name='User'
        )
        print(f"✓ 超级用户创建成功: {admin_user.username}")
        print("  用户名: admin")
        print("  密码: admin123")
        return True
    except Exception as e:
        print(f"✗ 创建超级用户失败: {e}")
        return False

def initialize_data():
    """初始化基础数据"""
    print("\n初始化基础数据...")
    
    try:
        # 运行初始化数据脚本
        from utils.init_data import initialize_system_data
        initialize_system_data()
        print("✓ 基础数据初始化完成")
        return True
    except Exception as e:
        print(f"✗ 初始化数据失败: {e}")
        return False

def start_server(port=8000):
    """启动开发服务器"""
    print(f"\n启动开发服务器 (端口: {port})...")
    print("="*50)
    print("密码管理系统开发服务器")
    print("="*50)
    print(f"服务器地址: http://localhost:{port}")
    print(f"管理后台: http://localhost:{port}/admin")
    print(f"API文档: http://localhost:{port}/api/docs/")
    print("="*50)
    print("按 Ctrl+C 停止服务器")
    print("="*50)
    
    try:
        execute_from_command_line(['manage.py', 'runserver', f'0.0.0.0:{port}'])
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"\n启动服务器失败: {e}")

def main():
    """主函数"""
    print("密码管理系统 - 开发服务器启动脚本")
    print("="*50)
    
    # 检查依赖
    if not check_requirements():
        return
    
    # 设置数据库
    if not setup_database():
        return
    
    # 创建超级用户
    if not create_superuser():
        return
    
    # 初始化数据
    if not initialize_data():
        print("警告: 基础数据初始化失败，但服务器仍可启动")
    
    # 启动服务器
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8000
    start_server(port)

if __name__ == '__main__':
    main()