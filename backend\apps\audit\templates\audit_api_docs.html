<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审计模块 API 文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .endpoint {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }
        .get { background-color: #28a745; }
        .post { background-color: #007bff; }
        .put { background-color: #ffc107; color: #212529; }
        .delete { background-color: #dc3545; }
        .field-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .field-table th,
        .field-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        .field-table th {
            background-color: #e9ecef;
            font-weight: 600;
        }
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            overflow-x: auto;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>审计模块 API 文档</h1>
        
        <div class="warning">
            <strong>重要提示：</strong> 本文档展示了审计模块API的正确字段名称。在编写前端代码时，请务必使用这里列出的字段名称，而不是假设的字段名称。
        </div>

        <h2>基础信息</h2>
        <ul>
            <li><strong>基础URL：</strong> http://localhost:8001/api/audit/</li>
            <li><strong>认证方式：</strong> JWT Token (Authorization: Bearer &lt;token&gt;)</li>
            <li><strong>响应格式：</strong> JSON</li>
            <li><strong>时间格式：</strong> ISO 8601 (例如: 2025-08-01T12:30:45.123456+08:00)</li>
        </ul>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /api/audit/operation-logs/</h3>
            <p>获取操作日志列表</p>
            
            <h4>查询参数</h4>
            <table class="field-table">
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>start_time</td>
                        <td>datetime</td>
                        <td>否</td>
                        <td>开始时间过滤 (ISO 8601格式)</td>
                    </tr>
                    <tr>
                        <td>end_time</td>
                        <td>datetime</td>
                        <td>否</td>
                        <td>结束时间过滤 (ISO 8601格式)</td>
                    </tr>
                    <tr>
                        <td>action_type</td>
                        <td>string</td>
                        <td>否</td>
                        <td>操作类型过滤</td>
                    </tr>
                    <tr>
                        <td>target_type</td>
                        <td>string</td>
                        <td>否</td>
                        <td>目标对象类型过滤</td>
                    </tr>
                    <tr>
                        <td>result</td>
                        <td>string</td>
                        <td>否</td>
                        <td>操作结果过滤 (success/failure)</td>
                    </tr>
                </tbody>
            </table>

            <h4>响应字段 (OperationLog)</h4>
            <table class="field-table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td>string</td>
                        <td>日志唯一标识</td>
                    </tr>
                    <tr>
                        <td>user</td>
                        <td>integer</td>
                        <td>操作用户ID</td>
                    </tr>
                    <tr>
                        <td>user_name</td>
                        <td>string</td>
                        <td>操作用户全名</td>
                    </tr>
                    <tr>
                        <td>user_email</td>
                        <td>string</td>
                        <td>操作用户邮箱</td>
                    </tr>
                    <tr>
                        <td>action_type</td>
                        <td>string</td>
                        <td>操作类型 (create, update, delete等)</td>
                    </tr>
                    <tr>
                        <td>action_display</td>
                        <td>string</td>
                        <td>操作类型显示名称</td>
                    </tr>
                    <tr>
                        <td>result</td>
                        <td>string</td>
                        <td>操作结果 (success/failure)</td>
                    </tr>
                    <tr>
                        <td>target_type</td>
                        <td>string</td>
                        <td>目标对象类型</td>
                    </tr>
                    <tr>
                        <td>target_id</td>
                        <td>string</td>
                        <td>目标对象ID</td>
                    </tr>
                    <tr>
                        <td>target_name</td>
                        <td>string</td>
                        <td>目标对象名称</td>
                    </tr>
                    <tr>
                        <td>description</td>
                        <td>string</td>
                        <td>操作描述</td>
                    </tr>
                    <tr>
                        <td>extra_data</td>
                        <td>object</td>
                        <td>额外数据 (JSON格式)</td>
                    </tr>
                    <tr>
                        <td>ip_address</td>
                        <td>string</td>
                        <td>操作者IP地址</td>
                    </tr>
                    <tr>
                        <td>user_agent</td>
                        <td>string</td>
                        <td>用户代理字符串</td>
                    </tr>
                    <tr>
                        <td>request_method</td>
                        <td>string</td>
                        <td>HTTP请求方法</td>
                    </tr>
                    <tr>
                        <td>request_path</td>
                        <td>string</td>
                        <td>请求路径</td>
                    </tr>
                    <tr>
                        <td><strong>created_at</strong></td>
                        <td>datetime</td>
                        <td><strong>创建时间 (注意：不是timestamp!)</strong></td>
                    </tr>
                </tbody>
            </table>

            <h4>示例响应</h4>
            <div class="code">
{
  "count": 1,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "user": 1,
      "user_name": "张三",
      "user_email": "<EMAIL>",
      "action_type": "create",
      "action_display": "创建",
      "result": "success",
      "target_type": "password_entry",
      "target_id": "456",
      "target_name": "测试密码",
      "description": "创建了新的密码条目",
      "extra_data": {"category": "工作"},
      "ip_address": "*************",
      "user_agent": "Mozilla/5.0...",
      "request_method": "POST",
      "request_path": "/api/passwords/",
      "created_at": "2025-08-01T12:30:45.123456+08:00"
    }
  ]
}
            </div>
        </div>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /api/audit/access-logs/</h3>
            <p>获取密码访问日志列表</p>
            
            <h4>响应字段 (PasswordAccessLog)</h4>
            <table class="field-table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td>string</td>
                        <td>日志唯一标识</td>
                    </tr>
                    <tr>
                        <td>user</td>
                        <td>integer</td>
                        <td>访问用户ID</td>
                    </tr>
                    <tr>
                        <td>user_name</td>
                        <td>string</td>
                        <td>访问用户全名</td>
                    </tr>
                    <tr>
                        <td>user_email</td>
                        <td>string</td>
                        <td>访问用户邮箱</td>
                    </tr>
                    <tr>
                        <td>password_entry</td>
                        <td>integer</td>
                        <td>密码条目ID</td>
                    </tr>
                    <tr>
                        <td>password_title</td>
                        <td>string</td>
                        <td>密码条目标题</td>
                    </tr>
                    <tr>
                        <td>access_type</td>
                        <td>string</td>
                        <td>访问类型</td>
                    </tr>
                    <tr>
                        <td>access_type_display</td>
                        <td>string</td>
                        <td>访问类型显示名称</td>
                    </tr>
                    <tr>
                        <td>ip_address</td>
                        <td>string</td>
                        <td>访问者IP地址</td>
                    </tr>
                    <tr>
                        <td>user_agent</td>
                        <td>string</td>
                        <td>用户代理字符串</td>
                    </tr>
                    <tr>
                        <td>access_source</td>
                        <td>string</td>
                        <td>访问来源</td>
                    </tr>
                    <tr>
                        <td>source_id</td>
                        <td>string</td>
                        <td>来源ID</td>
                    </tr>
                    <tr>
                        <td><strong>created_at</strong></td>
                        <td>datetime</td>
                        <td><strong>创建时间 (注意：不是timestamp!)</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>重要提醒</h2>
        <div class="warning">
            <h3>字段名称对照表</h3>
            <table class="field-table">
                <thead>
                    <tr>
                        <th>❌ 错误的字段名</th>
                        <th>✅ 正确的字段名</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>timestamp</td>
                        <td><strong>created_at</strong></td>
                        <td>时间戳字段</td>
                    </tr>
                    <tr>
                        <td>action</td>
                        <td><strong>action_type</strong></td>
                        <td>操作类型</td>
                    </tr>
                    <tr>
                        <td>resource_type</td>
                        <td><strong>target_type</strong></td>
                        <td>目标类型</td>
                    </tr>
                    <tr>
                        <td>resource_id</td>
                        <td><strong>target_id</strong></td>
                        <td>目标ID</td>
                    </tr>
                    <tr>
                        <td>success</td>
                        <td><strong>result</strong></td>
                        <td>操作结果 (值为"success"或"failure")</td>
                    </tr>
                    <tr>
                        <td>details</td>
                        <td><strong>extra_data</strong></td>
                        <td>额外数据</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
