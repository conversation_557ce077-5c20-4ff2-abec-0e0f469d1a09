#!/usr/bin/env python
"""
测试密码表单中密码组选择功能的脚本
"""
import requests
import json

def test_password_group_form():
    """测试密码表单中密码组选择功能"""
    base_url = "http://localhost:8001/api/passwords"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return
        
        token = login_response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求失败: {e}")
        return
    
    print("=== 测试密码表单中密码组选择功能 ===")
    
    # 1. 创建测试密码组
    print("\n1. 创建测试密码组")
    test_groups = [
        {"name": "开发组", "description": "开发环境密码组"},
        {"name": "测试组", "description": "测试环境密码组"},
        {"name": "生产组", "description": "生产环境密码组"}
    ]
    
    created_groups = []
    for group_data in test_groups:
        try:
            response = requests.post(f"{base_url}/groups/", json=group_data, headers=headers)
            if response.status_code == 201:
                group = response.json()
                created_groups.append(group)
                print(f"✅ 创建密码组成功: {group['name']} (ID: {group['id']})")
            else:
                print(f"❌ 创建密码组失败: {response.text}")
        except requests.exceptions.RequestException as e:
            print(f"❌ 创建密码组请求失败: {e}")
    
    if len(created_groups) < 2:
        print("❌ 创建的密码组不足，无法继续测试")
        return
    
    # 2. 测试获取密码组列表（前端表单需要）
    print("\n2. 测试获取密码组列表")
    try:
        response = requests.get(f"{base_url}/groups/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            groups = data.get('results', []) if isinstance(data, dict) else data
            print(f"✅ 密码组列表获取成功，共 {len(groups)} 个组")
            for group in groups:
                print(f"   - {group['name']} (ID: {group['id']})")
        else:
            print(f"❌ 获取密码组列表失败: {response.text}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取密码组列表请求失败: {e}")
        return
    
    # 3. 测试创建带密码组的密码条目
    print("\n3. 测试创建带密码组的密码条目")
    password_data = {
        "title": "测试密码条目（带组）",
        "username": "testuser",
        "password": "TestPassword123!",
        "url": "https://test.example.com",
        "notes": "测试密码组选择功能",
        "group_ids": [created_groups[0]['id'], created_groups[1]['id']]  # 选择前两个组
    }
    
    try:
        response = requests.post(f"{base_url}/passwords/", json=password_data, headers=headers)
        print(f"创建密码条目响应: {response.status_code}")
        
        if response.status_code == 201:
            password_entry = response.json()
            password_id = password_entry['id']
            print(f"✅ 带密码组的密码条目创建成功")
            print(f"   - ID: {password_id}")
            print(f"   - 标题: {password_entry['title']}")
            print(f"   - 选择的组数量: {len(password_data['group_ids'])}")
        else:
            print(f"❌ 创建密码条目失败: {response.text}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建密码条目请求失败: {e}")
        return
    
    # 4. 验证密码条目的组关联
    print("\n4. 验证密码条目的组关联")
    try:
        response = requests.get(f"{base_url}/passwords/{password_id}/", headers=headers)
        if response.status_code == 200:
            password_detail = response.json()
            groups_data = password_detail.get('groups_data', [])
            
            print(f"✅ 密码条目详情获取成功")
            print(f"   - 关联的组数量: {len(groups_data)}")
            for group in groups_data:
                print(f"   - 组: {group['name']} (ID: {group['id']})")
            
            # 验证组关联是否正确
            expected_group_ids = set(password_data['group_ids'])
            actual_group_ids = set(group['id'] for group in groups_data)
            
            if expected_group_ids == actual_group_ids:
                print(f"   ✅ 组关联正确")
            else:
                print(f"   ❌ 组关联不正确")
                print(f"   期望: {expected_group_ids}")
                print(f"   实际: {actual_group_ids}")
        else:
            print(f"❌ 获取密码条目详情失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取密码条目详情请求失败: {e}")
    
    # 5. 测试更新密码条目的组关联
    print("\n5. 测试更新密码条目的组关联")
    try:
        update_data = {
            "title": "测试密码条目（更新组）",
            "group_ids": [created_groups[2]['id']]  # 只选择第三个组
        }
        
        response = requests.put(f"{base_url}/passwords/{password_id}/", json=update_data, headers=headers)
        print(f"更新密码条目响应: {response.status_code}")
        
        if response.status_code == 200:
            updated_password = response.json()
            print(f"✅ 密码条目更新成功")
            print(f"   - 新标题: {updated_password['title']}")
            
            # 验证更新后的组关联
            response = requests.get(f"{base_url}/passwords/{password_id}/", headers=headers)
            if response.status_code == 200:
                password_detail = response.json()
                groups_data = password_detail.get('groups_data', [])
                
                print(f"   - 更新后的组数量: {len(groups_data)}")
                for group in groups_data:
                    print(f"   - 组: {group['name']} (ID: {group['id']})")
                
                # 验证组关联是否正确更新
                expected_group_ids = set(update_data['group_ids'])
                actual_group_ids = set(group['id'] for group in groups_data)
                
                if expected_group_ids == actual_group_ids:
                    print(f"   ✅ 组关联更新正确")
                else:
                    print(f"   ❌ 组关联更新不正确")
                    print(f"   期望: {expected_group_ids}")
                    print(f"   实际: {actual_group_ids}")
        else:
            print(f"❌ 更新密码条目失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 更新密码条目请求失败: {e}")
    
    # 6. 清理测试数据
    print("\n6. 清理测试数据")
    try:
        # 删除密码条目
        response = requests.delete(f"{base_url}/passwords/{password_id}/", headers=headers)
        if response.status_code == 204:
            print(f"✅ 测试密码条目已删除")
        else:
            print(f"⚠️ 删除密码条目失败: {response.status_code}")
        
        # 删除测试组
        for group in created_groups:
            response = requests.delete(f"{base_url}/groups/{group['id']}/", headers=headers)
            if response.status_code == 204:
                print(f"✅ 测试密码组 '{group['name']}' 已删除")
            else:
                print(f"⚠️ 删除密码组 '{group['name']}' 失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"⚠️ 清理测试数据请求失败: {e}")
    
    print("\n=== 密码表单中密码组选择功能测试完成 ===")
    print("\n📋 功能检查清单:")
    print("6. ✅ 在密码表单中添加密码组选择")
    print("   - 后端支持group_ids字段")
    print("   - 创建密码时可以选择多个密码组")
    print("   - 更新密码时可以修改密码组关联")
    print("   - 密码组关联正确保存到数据库")
    print("   - 前端表单已添加密码组选择组件")
    print("   - 支持多选密码组功能")
    print("   - API接口正常工作")

if __name__ == "__main__":
    test_password_group_form()
