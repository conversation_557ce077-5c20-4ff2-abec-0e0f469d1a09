#!/usr/bin/env python
"""
测试分享链接URL生成
"""
import os
import sys
import django
from unittest.mock import Mock

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.sharing.serializers import ShareLinkSerializer
from apps.sharing.models import OneTimeLink

def test_share_url_generation():
    print("测试分享链接URL生成...")
    
    # 创建模拟的OneTimeLink对象
    mock_link = Mock()
    mock_link.token = "test_token_123"
    
    # 测试1: 无请求上下文
    serializer = ShareLinkSerializer()
    url1 = serializer.get_share_url(mock_link)
    print(f"无请求上下文: {url1}")
    
    # 测试2: 有请求上下文，但无Origin头
    mock_request = Mock()
    mock_request.META = {}
    serializer = ShareLinkSerializer(context={'request': mock_request})
    url2 = serializer.get_share_url(mock_link)
    print(f"无Origin头: {url2}")
    
    # 测试3: 有前端Origin头
    mock_request.META = {'HTTP_ORIGIN': 'http://localhost:5666'}
    serializer = ShareLinkSerializer(context={'request': mock_request})
    url3 = serializer.get_share_url(mock_link)
    print(f"有前端Origin头: {url3}")
    
    # 测试4: 有后端Origin头（应该使用默认前端URL）
    mock_request.META = {'HTTP_ORIGIN': 'http://localhost:8001'}
    serializer = ShareLinkSerializer(context={'request': mock_request})
    url4 = serializer.get_share_url(mock_link)
    print(f"有后端Origin头: {url4}")
    
    print("\n预期结果:")
    print("- 所有URL都应该指向前端端口 (5666)")
    print("- URL格式: http://localhost:5666/share/test_token_123/")

if __name__ == '__main__':
    test_share_url_generation()
