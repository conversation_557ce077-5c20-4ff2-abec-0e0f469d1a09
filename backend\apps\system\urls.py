from django.urls import path
from rest_framework.routers import DefaultRouter
from . import views

# 系统设置相关URL
setting_urlpatterns = [
    path('settings/', views.SystemSettingListView.as_view(), name='system_setting_list'),
    path('settings/<str:key>/', views.SystemSettingDetailView.as_view(), name='system_setting_detail'),
    path('settings/batch/update/', views.SystemSettingBatchUpdateView.as_view(), name='system_setting_batch_update'),
]

# 邮件模板相关URL
email_template_urlpatterns = [
    path('email-templates/', views.EmailTemplateListCreateView.as_view(), name='email_template_list_create'),
    path('email-templates/<int:pk>/', views.EmailTemplateDetailView.as_view(), name='email_template_detail'),
]

# 备份配置相关URL
backup_config_urlpatterns = [
    path('backup-configs/', views.BackupConfigListCreateView.as_view(), name='backup_config_list_create'),
    path('backup-configs/<int:pk>/', views.BackupConfigDetailView.as_view(), name='backup_config_detail'),
]

# 系统状态和维护相关URL
system_urlpatterns = [
    path('status/', views.SystemStatusView.as_view(), name='system_status'),
    path('maintenance/', views.SystemMaintenanceView.as_view(), name='system_maintenance'),
]

urlpatterns = (
    setting_urlpatterns +
    email_template_urlpatterns +
    backup_config_urlpatterns +
    system_urlpatterns
)

# 使用DRF路由器
router = DefaultRouter()
# router.register(r'settings', SystemSettingViewSet)

urlpatterns += router.urls