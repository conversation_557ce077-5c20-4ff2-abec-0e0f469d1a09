#!/usr/bin/env python
"""
测试搜索功能
"""
import os
import sys
import django
from django.conf import settings

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordEntry
from django.db.models import Q

User = get_user_model()

def test_search():
    """测试搜索功能"""
    try:
        user = User.objects.get(username='admin')
    except User.DoesNotExist:
        print("admin用户不存在")
        return
    
    print("=== 搜索功能测试 ===")
    
    # 测试按标题搜索
    print("\n1. 按标题搜索 '用户管理':")
    results = PasswordEntry.objects.filter(
        owner=user,
        title__icontains='用户管理'
    )[:5]
    for p in results:
        print(f"  - {p.title} | {p.username} | {p.hostname}")
    
    # 测试按用户名搜索
    print("\n2. 按用户名搜索 'admin':")
    results = PasswordEntry.objects.filter(
        owner=user,
        username__icontains='admin'
    )[:5]
    for p in results:
        print(f"  - {p.title} | {p.username} | {p.hostname}")
    
    # 测试按主机名搜索
    print("\n3. 按主机名搜索 '192.168':")
    results = PasswordEntry.objects.filter(
        owner=user,
        hostname__icontains='192.168'
    )[:5]
    for p in results:
        print(f"  - {p.title} | {p.username} | {p.hostname}")
    
    # 测试综合搜索（模拟DRF的SearchFilter）
    print("\n4. 综合搜索 'root':")
    search_query = 'root'
    results = PasswordEntry.objects.filter(
        owner=user
    ).filter(
        Q(title__icontains=search_query) |
        Q(username__icontains=search_query) |
        Q(hostname__icontains=search_query) |
        Q(url__icontains=search_query) |
        Q(notes__icontains=search_query)
    )[:5]
    for p in results:
        print(f"  - {p.title} | {p.username} | {p.hostname}")
    
    print(f"\n总密码数量: {PasswordEntry.objects.filter(owner=user).count()}")

if __name__ == '__main__':
    test_search()
