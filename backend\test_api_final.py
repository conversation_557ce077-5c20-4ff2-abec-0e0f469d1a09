#!/usr/bin/env python
"""
最终API测试脚本
"""
import requests
import json


def test_final_api():
    """最终API测试"""
    base_url = "http://localhost:8001/api/passwords"

    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}

    login_response = requests.post(
        "http://localhost:8001/api/auth/login/", json=login_data
    )
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return

    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}

    print("=== 最终API测试 ===")

    # 1. 测试获取所有组
    print("\n1. 获取所有组")
    response = requests.get(f"{base_url}/groups/", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        groups = response.json()
        print(f"组数量: {groups['count']}")
        for group in groups["results"]:
            print(f"- {group['name']}: {group['description']}")
            print(
                f"  成员数: {group['members_count']}, 密码数: {group['password_entries_count']}"
            )

    # 2. 测试获取组权限
    print("\n2. 获取组权限")
    response = requests.get(f"{base_url}/group-permissions/", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        permissions = response.json()
        print(f"权限数量: {permissions['count']}")
        for perm in permissions["results"]:
            print(
                f"- {perm['username']} 在 {perm['group_name']} 中的权限: {perm['permission_display']}"
            )

    # 3. 测试获取密码列表（包含组信息）
    print("\n3. 获取密码列表（包含组信息）")
    response = requests.get(f"{base_url}/passwords/?page_size=5", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        passwords = response.json()
        print(f"密码数量: {passwords['count']}")

        for password in passwords["results"]:
            print(f"\n密码: {password['title']}")
            if password.get("groups_data"):
                print(f"  所属组: {[g['name'] for g in password['groups_data']]}")
            if password.get("user_groups_permissions"):
                perms = [
                    f"{p['group_name']}({p['permission']})"
                    for p in password["user_groups_permissions"]
                ]
                print(f"  用户权限: {perms}")

    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    test_final_api()
