#!/usr/bin/env python
"""
创建默认密码策略的脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordPolicy

User = get_user_model()

def create_default_password_policy():
    """创建默认密码策略"""
    print("=== 创建默认密码策略 ===")
    
    # 获取管理员用户
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ 未找到管理员用户，请先创建管理员用户")
        return
    
    # 检查是否已存在默认策略
    existing_default = PasswordPolicy.objects.filter(is_default=True).first()
    if existing_default:
        print(f"✅ 默认密码策略已存在: {existing_default.name}")
        return
    
    # 创建默认密码策略
    default_policy = PasswordPolicy.objects.create(
        name="默认密码策略",
        description="系统默认的密码策略，适用于一般用途的密码生成和验证",
        min_length=12,
        max_length=128,
        uppercase_count=1,
        lowercase_count=1,
        digit_count=1,
        special_char_count=1,
        allowed_special_chars="!@#$%^&*()_+-=[]{}|;:,.<>?",
        allow_repeated_chars=False,
        max_repeated_chars=2,
        forbid_common_passwords=True,
        forbid_personal_info=True,
        is_active=True,
        is_default=True,
        created_by=admin_user
    )
    
    print(f"✅ 默认密码策略创建成功: {default_policy.name}")
    print(f"   - 最小长度: {default_policy.min_length}")
    print(f"   - 大写字母: {default_policy.uppercase_count}")
    print(f"   - 小写字母: {default_policy.lowercase_count}")
    print(f"   - 数字: {default_policy.digit_count}")
    print(f"   - 特殊字符: {default_policy.special_char_count}")
    
    # 创建几个额外的策略示例
    policies = [
        {
            "name": "高强度密码策略",
            "description": "适用于高安全要求的系统，密码复杂度较高",
            "min_length": 16,
            "uppercase_count": 2,
            "lowercase_count": 2,
            "digit_count": 2,
            "special_char_count": 2,
        },
        {
            "name": "简单密码策略",
            "description": "适用于内部测试环境，密码要求较低",
            "min_length": 8,
            "uppercase_count": 1,
            "lowercase_count": 1,
            "digit_count": 1,
            "special_char_count": 0,
        },
        {
            "name": "数据库密码策略",
            "description": "适用于数据库连接密码，不包含特殊字符",
            "min_length": 14,
            "uppercase_count": 2,
            "lowercase_count": 2,
            "digit_count": 2,
            "special_char_count": 0,
            "allowed_special_chars": "",
        }
    ]
    
    for policy_data in policies:
        policy = PasswordPolicy.objects.create(
            name=policy_data["name"],
            description=policy_data["description"],
            min_length=policy_data["min_length"],
            max_length=128,
            uppercase_count=policy_data["uppercase_count"],
            lowercase_count=policy_data["lowercase_count"],
            digit_count=policy_data["digit_count"],
            special_char_count=policy_data["special_char_count"],
            allowed_special_chars=policy_data.get("allowed_special_chars", "!@#$%^&*()_+-=[]{}|;:,.<>?"),
            allow_repeated_chars=False,
            max_repeated_chars=2,
            forbid_common_passwords=True,
            forbid_personal_info=True,
            is_active=True,
            is_default=False,
            created_by=admin_user
        )
        print(f"✅ 密码策略创建成功: {policy.name}")
    
    print(f"\n=== 密码策略创建完成 ===")
    print(f"总共创建了 {PasswordPolicy.objects.count()} 个密码策略")

if __name__ == "__main__":
    create_default_password_policy()
