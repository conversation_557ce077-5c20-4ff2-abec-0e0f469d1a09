#!/usr/bin/env python
"""
测试部门管理上级部门选择修复的脚本
"""
import requests
import json
import time

def test_department_parent_fix():
    """测试部门管理上级部门选择修复"""
    base_url = "http://localhost:8001/api/users"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试部门管理上级部门选择修复 ===")
    
    # 1. 创建父部门
    print("\n1. 创建父部门")
    parent_dept_data = {
        "name": f"父部门_{int(time.time())}",
        "description": "这是一个父部门"
    }
    
    response = requests.post(f"{base_url}/departments/", json=parent_dept_data, headers=headers)
    print(f"创建父部门响应: {response.status_code}")
    if response.status_code == 201:
        parent_dept = response.json()
        print(f"✅ 父部门创建成功: {parent_dept['name']}")
        parent_dept_id = parent_dept['id']
        
        # 2. 创建子部门（指定上级部门）
        print("\n2. 创建子部门（指定上级部门）")
        child_dept_data = {
            "name": f"子部门_{int(time.time())}",
            "description": "这是一个子部门",
            "parent": parent_dept_id  # 直接传递主键值
        }
        
        response = requests.post(f"{base_url}/departments/", json=child_dept_data, headers=headers)
        print(f"创建子部门响应: {response.status_code}")
        if response.status_code == 201:
            child_dept = response.json()
            print(f"✅ 子部门创建成功: {child_dept['name']}")
            print(f"   - 上级部门ID: {child_dept.get('parent')}")
            child_dept_id = child_dept['id']
            
            # 3. 获取子部门详情验证层级关系
            print("\n3. 验证层级关系")
            response = requests.get(f"{base_url}/departments/{child_dept_id}/", headers=headers)
            if response.status_code == 200:
                child_detail = response.json()
                print(f"✅ 子部门详情获取成功:")
                print(f"   - 子部门名称: {child_detail.get('name')}")
                print(f"   - 上级部门ID: {child_detail.get('parent')}")
                
                # 验证上级部门信息
                if child_detail.get('parent') == parent_dept_id:
                    print(f"✅ 层级关系正确：子部门的上级部门ID匹配")
                else:
                    print(f"❌ 层级关系错误：期望 {parent_dept_id}，实际 {child_detail.get('parent')}")
            else:
                print(f"❌ 获取子部门详情失败: {response.text}")
            
            # 4. 更新子部门的上级部门
            print("\n4. 测试更新子部门的上级部门")
            update_data = {
                "name": child_dept['name'],
                "description": "更新后的子部门描述",
                "parent": None  # 移除上级部门
            }
            
            response = requests.put(f"{base_url}/departments/{child_dept_id}/", json=update_data, headers=headers)
            print(f"更新子部门响应: {response.status_code}")
            if response.status_code == 200:
                updated_child = response.json()
                print(f"✅ 子部门更新成功")
                print(f"   - 更新后上级部门: {updated_child.get('parent')}")
                
                if updated_child.get('parent') is None:
                    print(f"✅ 上级部门移除成功")
                else:
                    print(f"❌ 上级部门移除失败")
            else:
                print(f"❌ 更新子部门失败: {response.text}")
            
            # 清理测试数据
            print(f"\n5. 清理测试数据")
            # 删除子部门
            response = requests.delete(f"{base_url}/departments/{child_dept_id}/", headers=headers)
            if response.status_code == 204:
                print("✅ 子部门删除成功")
            else:
                print(f"⚠️ 删除子部门失败: {response.text}")
        else:
            print(f"❌ 创建子部门失败: {response.text}")
            print(f"   错误详情: {response.json() if response.content else '无详情'}")
        
        # 删除父部门
        response = requests.delete(f"{base_url}/departments/{parent_dept_id}/", headers=headers)
        if response.status_code == 204:
            print("✅ 父部门删除成功")
        else:
            print(f"⚠️ 删除父部门失败: {response.text}")
    else:
        print(f"❌ 创建父部门失败: {response.text}")
    
    print("\n=== 部门管理上级部门选择修复测试完成 ===")
    print("\n📋 修复检查清单:")
    print("1. ✅ 修复前端数据提交格式")
    print("   - parent字段直接传递主键值而不是对象")
    print("   - 支持创建带上级部门的子部门")
    print("   - 支持更新部门的上级部门关系")
    print("   - 支持移除上级部门关系")

if __name__ == "__main__":
    test_department_parent_fix()
