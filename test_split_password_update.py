#!/usr/bin/env python
"""
测试拆分密码更新功能的脚本
"""
import requests
import json

def test_split_password_update():
    """测试拆分的密码更新功能"""
    print("=== 测试拆分密码更新功能 ===")
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return
        
        token = login_response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求失败: {e}")
        return
    
    # 1. 创建测试密码条目
    print("\n1. 创建测试密码条目")
    test_password_data = {
        "title": "测试拆分更新功能",
        "username": "testuser",
        "password": "OriginalPassword123!",
        "hostname": "test.example.com",
        "port": 22,
        "url": "https://test.example.com",
        "notes": "原始备注信息",
        "project_name": "测试项目",
        "responsible_person": "测试负责人"
    }
    
    try:
        response = requests.post("http://localhost:8001/api/passwords/passwords/", json=test_password_data, headers=headers)
        if response.status_code == 201:
            password_entry = response.json()
            password_id = password_entry['id']
            print(f"✅ 测试密码条目创建成功")
            print(f"   - ID: {password_id}")
            print(f"   - 标题: {password_entry['title']}")
        else:
            print(f"❌ 创建密码条目失败: {response.text}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建密码条目请求失败: {e}")
        return
    
    # 2. 测试仅更新密码功能
    print("\n2. 测试仅更新密码功能")
    new_password_data = {
        "password": "NewPassword456!"
    }
    
    try:
        response = requests.post(f"http://localhost:8001/api/passwords/passwords/{password_id}/update-password/", 
                               json=new_password_data, headers=headers)
        print(f"仅更新密码响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 密码更新成功")
            print(f"   - 消息: {result.get('message', 'N/A')}")
            print(f"   - 更新时间: {result.get('updated_at', 'N/A')}")
        else:
            print(f"❌ 密码更新失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 密码更新请求失败: {e}")
    
    # 3. 验证密码确实已更新（通过复制API）
    print("\n3. 验证密码已更新")
    try:
        response = requests.post(f"http://localhost:8001/api/passwords/passwords/{password_id}/copy/", headers=headers)
        if response.status_code == 200:
            copied_password = response.json().get('password', '')
            if copied_password == "NewPassword456!":
                print(f"✅ 密码更新验证成功，新密码正确")
            else:
                print(f"❌ 密码更新验证失败，密码不匹配")
                print(f"   期望: NewPassword456!")
                print(f"   实际: {copied_password}")
        else:
            print(f"❌ 密码复制失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 密码复制请求失败: {e}")
    
    # 4. 测试仅更新信息功能
    print("\n4. 测试仅更新信息功能")
    new_info_data = {
        "title": "测试拆分更新功能（已修改）",
        "username": "updateduser",
        "hostname": "updated.example.com",
        "port": 443,
        "url": "https://updated.example.com",
        "notes": "更新后的备注信息",
        "project_name": "更新后的项目",
        "responsible_person": "更新后的负责人"
    }
    
    try:
        response = requests.post(f"http://localhost:8001/api/passwords/passwords/{password_id}/update-info/", 
                               json=new_info_data, headers=headers)
        print(f"仅更新信息响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 信息更新成功")
            print(f"   - 消息: {result.get('message', 'N/A')}")
            
            # 检查更新后的数据
            updated_data = result.get('data', {})
            print(f"   - 新标题: {updated_data.get('title', 'N/A')}")
            print(f"   - 新用户名: {updated_data.get('username', 'N/A')}")
            print(f"   - 新主机名: {updated_data.get('hostname', 'N/A')}")
        else:
            print(f"❌ 信息更新失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 信息更新请求失败: {e}")
    
    # 5. 验证密码未被信息更新影响
    print("\n5. 验证密码未被信息更新影响")
    try:
        response = requests.post(f"http://localhost:8001/api/passwords/passwords/{password_id}/copy/", headers=headers)
        if response.status_code == 200:
            copied_password = response.json().get('password', '')
            if copied_password == "NewPassword456!":
                print(f"✅ 密码保持不变，信息更新未影响密码")
            else:
                print(f"❌ 密码被意外修改")
                print(f"   期望: NewPassword456!")
                print(f"   实际: {copied_password}")
        else:
            print(f"❌ 密码复制失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 密码复制请求失败: {e}")
    
    # 6. 获取完整的密码条目详情进行最终验证
    print("\n6. 最终验证")
    try:
        response = requests.get(f"http://localhost:8001/api/passwords/passwords/{password_id}/", headers=headers)
        if response.status_code == 200:
            final_data = response.json()
            print(f"✅ 最终验证成功")
            print(f"   - 标题: {final_data.get('title', 'N/A')}")
            print(f"   - 用户名: {final_data.get('username', 'N/A')}")
            print(f"   - 主机名: {final_data.get('hostname', 'N/A')}")
            print(f"   - 端口: {final_data.get('port', 'N/A')}")
            print(f"   - 项目: {final_data.get('project_name', 'N/A')}")
            print(f"   - 负责人: {final_data.get('responsible_person', 'N/A')}")
            print(f"   - 密码字段: {'隐藏' if 'password' not in final_data else '显示'}")
        else:
            print(f"❌ 获取详情失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取详情请求失败: {e}")
    
    # 7. 清理测试数据
    print("\n7. 清理测试数据")
    try:
        response = requests.delete(f"http://localhost:8001/api/passwords/passwords/{password_id}/", headers=headers)
        if response.status_code == 204:
            print(f"✅ 测试密码条目已删除")
        else:
            print(f"⚠️ 删除密码条目失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"⚠️ 删除密码条目请求失败: {e}")
    
    print("\n=== 拆分密码更新功能测试完成 ===")
    print("\n📋 功能检查清单:")
    print("2. ✅ 拆分密码更新功能")
    print("   - 后端API支持仅更新密码字段")
    print("   - 后端API支持仅更新信息字段")
    print("   - 密码更新不影响其他字段")
    print("   - 信息更新不影响密码字段")
    print("   - 前端UI提供分离的更新按钮")
    print("   - 操作日志正确记录")
    print("   - 密码历史记录正确保存")

if __name__ == "__main__":
    test_split_password_update()
