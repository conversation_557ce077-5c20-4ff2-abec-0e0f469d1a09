#!/usr/bin/env python
"""
IT运维API测试脚本
"""
import os
import sys
import django
import json
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordEntry, Category, Tag
from utils.encryption import encrypt_data

User = get_user_model()


def create_test_data():
    """创建测试数据"""
    print("创建IT运维测试数据...")

    # 获取或创建测试用户
    user, created = User.objects.get_or_create(
        username="itops_test",
        defaults={
            "email": "<EMAIL>",
            "first_name": "IT运维",
            "last_name": "测试用户",
        },
    )
    if created:
        user.set_password("test123")
        user.save()
        print(f"创建测试用户: {user.username}")

    # 创建分类
    categories = [
        {"name": "生产环境", "description": "生产环境系统"},
        {"name": "测试环境", "description": "测试环境系统"},
        {"name": "数据库", "description": "数据库系统"},
        {"name": "中间件", "description": "中间件系统"},
    ]

    for cat_data in categories:
        category, created = Category.objects.get_or_create(
            name=cat_data["name"], user=user, defaults=cat_data
        )
        if created:
            print(f"创建分类: {category.name}")

    # 创建标签
    tags = [
        {"name": "重要", "color": "#ff4d4f"},
        {"name": "核心业务", "color": "#fa8c16"},
        {"name": "备份", "color": "#52c41a"},
    ]

    for tag_data in tags:
        tag, created = Tag.objects.get_or_create(
            name=tag_data["name"], user=user, defaults=tag_data
        )
        if created:
            print(f"创建标签: {tag.name}")

    # 创建IT运维密码条目
    password_entries = [
        {
            "title": "生产数据库主库",
            "username": "root",
            "password": "Prod@DB#2024!",
            "system_type": "database",
            "database_type": "mysql",
            "hostname": "*************",
            "port": 3306,
            "protocol": "mysql",
            "environment": "prod",
            "business_system": "核心业务系统",
            "responsible_person": "张三",
            "description": "生产环境核心业务数据库主库",
            "database_name": "core_business",
            "expires_at": datetime.now() + timedelta(days=90),
        },
        {
            "title": "Web服务器01",
            "username": "admin",
            "password": "WebServer@2024#",
            "system_type": "linux",
            "hostname": "************",
            "port": 22,
            "protocol": "ssh",
            "environment": "prod",
            "business_system": "Web前端",
            "responsible_person": "李四",
            "description": "Nginx Web服务器",
            "expires_at": datetime.now() + timedelta(days=60),
        },
        {
            "title": "Redis缓存集群",
            "username": "redis",
            "password": "Redis@Cache#2024",
            "system_type": "middleware",
            "database_type": "redis",
            "hostname": "*************",
            "port": 6379,
            "protocol": "redis",
            "environment": "prod",
            "business_system": "缓存系统",
            "responsible_person": "王五",
            "description": "Redis缓存集群主节点",
            "database_name": "0",
        },
        {
            "title": "测试环境数据库",
            "username": "testuser",
            "password": "test123",  # 弱密码
            "system_type": "database",
            "database_type": "mysql",
            "hostname": "*************",
            "port": 3306,
            "protocol": "mysql",
            "environment": "test",
            "business_system": "测试系统",
            "responsible_person": "赵六",
            "description": "测试环境数据库",
            "database_name": "test_db",
        },
        {
            "title": "监控系统",
            "username": "monitor",
            "password": "Monitor@2024!",
            "system_type": "application",
            "hostname": "************",
            "port": 8080,
            "protocol": "http",
            "url": "http://************:8080",
            "environment": "prod",
            "project_name": "监控平台",
            "responsible_person": "钱七",
            "description": "Prometheus监控系统",
        },
        {
            "title": "过期密码示例",
            "username": "expired",
            "password": "OldPassword123",
            "system_type": "linux",
            "hostname": "************",
            "port": 22,
            "protocol": "ssh",
            "environment": "test",
            "project_name": "旧系统",
            "responsible_person": "孙八",
            "description": "已过期的测试服务器",
            "expires_at": datetime.now() - timedelta(days=30),  # 已过期
        },
    ]

    for entry_data in password_entries:
        # 检查是否已存在
        existing = PasswordEntry.objects.filter(
            title=entry_data["title"], owner=user
        ).first()

        if not existing:
            entry_data["owner"] = user
            entry = PasswordEntry.objects.create(**entry_data)
            print(f"创建密码条目: {entry.title}")
        else:
            print(f"密码条目已存在: {existing.title}")

    print(f"测试数据创建完成！用户: {user.username}")
    return user


def test_itops_apis():
    """测试IT运维API"""
    print("\n测试IT运维API...")

    from django.test import Client
    from django.contrib.auth import authenticate

    client = Client()

    # 登录获取token
    response = client.post(
        "/api/auth/login/",
        {"username": "itops_test", "password": "test123"},
        content_type="application/json",
    )

    if response.status_code == 200:
        token_data = response.json()
        token = token_data.get("access_token")
        headers = {"HTTP_AUTHORIZATION": f"Bearer {token}"}

        # 测试系统类型API
        print("\n1. 测试系统类型API")
        response = client.get("/api/passwords/itops/system-types/", **headers)
        if response.status_code == 200:
            data = response.json()
            print(f"系统类型: {len(data['system_types'])}种")
            print(f"数据库类型: {len(data['database_types'])}种")
            print(f"协议类型: {len(data['protocols'])}种")

        # 测试密码列表API
        print("\n2. 测试密码列表API")
        response = client.get("/api/passwords/itops/passwords/", **headers)
        if response.status_code == 200:
            data = response.json()
            print(f"总密码数: {data['count']}")

        # 测试按系统类型筛选
        print("\n3. 测试按系统类型筛选")
        response = client.get(
            "/api/passwords/itops/passwords/?system_type=database", **headers
        )
        if response.status_code == 200:
            data = response.json()
            print(f"数据库密码数: {data['count']}")

        # 测试按环境筛选
        print("\n4. 测试按环境筛选")
        response = client.get(
            "/api/passwords/itops/passwords/?environment=prod", **headers
        )
        if response.status_code == 200:
            data = response.json()
            print(f"生产环境密码数: {data['count']}")

        # 测试统计API
        print("\n5. 测试统计API")
        response = client.get("/api/passwords/itops/stats/", **headers)
        if response.status_code == 200:
            data = response.json()
            print(f"总密码数: {data['total_passwords']}")
            print(f"弱密码数: {data['security_stats']['weak_passwords']}")
            print(f"过期密码数: {data['security_stats']['expired_passwords']}")
            print(f"即将过期密码数: {data['security_stats']['expiring_soon']}")

        # 测试安全分析API
        print("\n6. 测试安全分析API")
        response = client.get("/api/passwords/itops/security-analysis/", **headers)
        if response.status_code == 200:
            data = response.json()
            print(f"平均密码强度: {data['average_strength']}")
            print(f"IT运维建议数: {len(data['itops_recommendations'])}")
            for rec in data["itops_recommendations"]:
                print(f"  - {rec}")

        print("\nIT运维API测试完成！")
    else:
        print(f"登录失败: {response.status_code}")


if __name__ == "__main__":
    # 创建测试数据
    user = create_test_data()

    # 测试API
    test_itops_apis()

    print("\n测试完成！")
    print("可以通过以下URL访问API:")
    print("- 系统类型: http://localhost:8000/api/passwords/itops/system-types/")
    print("- 密码列表: http://localhost:8000/api/passwords/itops/passwords/")
    print("- 系统统计: http://localhost:8000/api/passwords/itops/stats/")
    print("- 安全分析: http://localhost:8000/api/passwords/itops/security-analysis/")
