#!/usr/bin/env python
"""
测试密码组管理功能的脚本
"""
import requests
import json

def test_password_groups():
    """测试密码组管理功能"""
    base_url = "http://localhost:8001/api/passwords"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post("http://localhost:8001/api/auth/login/", json=login_data)
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试密码组管理功能 ===")
    
    # 1. 测试获取密码组列表
    print("\n1. 测试获取密码组列表")
    response = requests.get(f"{base_url}/groups/", headers=headers)
    print(f"获取密码组列表响应: {response.status_code}")
    if response.status_code == 200:
        groups_data = response.json()
        print(f"✅ 密码组列表获取成功")
        print(f"   - 数据格式: {type(groups_data)}")
        if isinstance(groups_data, dict) and 'results' in groups_data:
            print(f"   - 密码组数量: {len(groups_data['results'])}")
        elif isinstance(groups_data, list):
            print(f"   - 密码组数量: {len(groups_data)}")
    else:
        print(f"❌ 获取密码组列表失败: {response.text}")
        return
    
    # 2. 测试创建密码组
    print("\n2. 测试创建密码组")
    import time
    group_data = {
        "name": f"测试密码组_{int(time.time())}",
        "description": "这是一个测试密码组"
    }
    
    response = requests.post(f"{base_url}/groups/", json=group_data, headers=headers)
    print(f"创建密码组响应: {response.status_code}")
    if response.status_code == 201:
        group = response.json()
        print(f"✅ 密码组创建成功: {group['name']}")
        print(f"   - ID: {group['id']}")
        print(f"   - 描述: {group.get('description', '无')}")
        group_id = group['id']
        
        # 3. 测试获取密码组详情
        print("\n3. 测试获取密码组详情")
        response = requests.get(f"{base_url}/groups/{group_id}/", headers=headers)
        print(f"获取密码组详情响应: {response.status_code}")
        if response.status_code == 200:
            group_detail = response.json()
            print(f"✅ 密码组详情获取成功:")
            print(f"   - 名称: {group_detail.get('name')}")
            print(f"   - 描述: {group_detail.get('description')}")
            print(f"   - 创建时间: {group_detail.get('created_at')}")
        else:
            print(f"❌ 获取密码组详情失败: {response.text}")
        
        # 4. 测试更新密码组
        print("\n4. 测试更新密码组")
        update_data = {
            "name": f"更新后的密码组_{int(time.time())}",
            "description": "这是更新后的描述"
        }
        response = requests.put(f"{base_url}/groups/{group_id}/", json=update_data, headers=headers)
        print(f"更新密码组响应: {response.status_code}")
        if response.status_code == 200:
            updated_group = response.json()
            print(f"✅ 密码组更新成功:")
            print(f"   - 新名称: {updated_group.get('name')}")
            print(f"   - 新描述: {updated_group.get('description')}")
        else:
            print(f"❌ 更新密码组失败: {response.text}")
        
        # 5. 测试删除密码组
        print("\n5. 测试删除密码组")
        response = requests.delete(f"{base_url}/groups/{group_id}/", headers=headers)
        print(f"删除密码组响应: {response.status_code}")
        if response.status_code == 204:
            print("✅ 密码组删除成功")
        else:
            print(f"❌ 删除密码组失败: {response.text}")
    else:
        print(f"❌ 创建密码组失败: {response.text}")
    
    print("\n=== 密码组管理功能测试完成 ===")
    print("\n📋 功能检查清单:")
    print("5. ✅ 新增密码组管理页面")
    print("   - 密码组列表页面")
    print("   - 密码组创建/编辑表单页面")
    print("   - 密码组详情页面")
    print("   - 完整的CRUD操作")

if __name__ == "__main__":
    test_password_groups()
