version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: locker_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: password_manager
      MYSQL_USER: locker_user
      MYSQL_PASSWORD: locker_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - locker_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: locker_backend
    restart: always
    environment:
      - DEBUG=True
      - SECRET_KEY=your-secret-key-for-development
      - DATABASE_URL=mysql://locker_user:locker_pass@mysql:3306/password_manager
      - ENCRYPTION_KEY=your-encryption-key-for-development
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_media:/app/media
      - backend_logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - locker_network

volumes:
  mysql_data:
    driver: local
  backend_media:
    driver: local
  backend_logs:
    driver: local

networks:
  locker_network:
    driver: bridge