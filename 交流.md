基于Augment Code的特性，以下是针对你的密码管理系统项目的具体用户指导示例：

## Augment Code 项目使用指南

### 1. 工作区上下文配置

```` path=.augmentignore mode=EDIT
# 忽略不需要索引的文件
node_modules/
.git/
__pycache__/
*.pyc
.env
.env.local
dist/
build/
coverage/
.pytest_cache/
.vscode/
.idea/
*.log
````

### 2. Chat 功能使用示例

#### 代码解释和分析
```
@backend/apps/users/models.py 这个用户模型的设计有什么优缺点？如何优化？
```

#### 跨文件代码理解
```
@backend/apps/users/views.py @backend/apps/users/serializers.py 
用户认证流程是如何实现的？MFA验证的完整流程是什么？
```

#### 功能实现指导
```
基于现有的用户模型和权限系统，帮我实现密码分享功能的权限控制逻辑。
需要考虑：
- 用户角色权限
- 部门和团队隔离
- 分享范围限制
```

### 3. Agent 功能使用示例

#### 完整功能开发
```
请帮我实现前端的密码生成器组件：

要求：
- 基于Vue3 + TypeScript
- 使用Ant Design Vue组件
- 支持自定义密码长度、字符集
- 实时显示密码强度
- 集成到现有的vben-admin框架中
- 放在 frontend/src/components/PasswordGenerator/ 目录下

参考现有的组件结构和代码风格。
```

#### 代码重构任务
```
重构 @backend/apps/passwords/views.py 中的密码管理视图：

目标：
- 优化查询性能
- 添加更完善的权限检查
- 统一错误处理格式
- 添加详细的API文档注释
- 保持现有功能不变
```

#### 测试代码生成
```
为 @backend/apps/users/models.py 中的User模型生成完整的单元测试：

要求：
- 使用Django TestCase
- 覆盖所有模型方法
- 测试字段验证
- 测试关联关系
- 测试权限相关逻辑
- 文件保存到 backend/tests/test_users.py
```

### 4. Next Edit 功能使用示例

#### 批量代码更新
选中多个相似的函数或类，使用 `Cmd/Ctrl + ;` 进行批量修改：

```python
# 选中这类重复的权限检查代码
if not request.user.has_perm('passwords.view_password'):
    return Response({'error': 'Permission denied'}, status=403)
```

然后使用Next Edit统一替换为更优雅的装饰器模式。

#### 代码风格统一
```python
# 选中需要统一格式的代码块
def get_user_passwords(self, user_id):
    # 使用Next Edit统一添加类型提示、文档字符串等
```

### 5. Instructions 功能使用示例

#### 自定义指令创建
```
名称：Django API View Generator
指令：
基于Django REST Framework创建API视图，包括：
- 完整的CRUD操作
- 权限检查装饰器
- 序列化器集成
- 错误处理
- API文档注释
- 符合项目代码规范

使用方法：选中模型类，按Cmd/Ctrl+I，输入"生成API视图"
```

#### 代码审查指令
```
名称：Security Code Review
指令：
审查选中的代码，重点检查：
- SQL注入风险
- XSS漏洞
- 权限控制缺陷
- 敏感数据泄露
- 加密算法使用
- 输入验证完整性

提供具体的修复建议和代码示例。
```

### 6. 项目特定的提示词模板

#### 前端组件开发
```
我需要开发一个Vue3组件用于密码管理系统：

组件名：[组件名称]
功能：[具体功能描述]
位置：frontend/src/components/[路径]

技术要求：
- 基于现有的 @frontend/src/components 结构
- 使用 @frontend/src/utils 中的工具函数
- 遵循 @frontend/src/types 中的类型定义
- 集成 @frontend/src/api 中的接口调用
- 符合项目的代码规范和设计风格

请参考现有组件的实现模式。
```

#### 后端API开发
```
基于现有的项目结构，实现新的API功能：

模块：backend/apps/[模块名]
功能：[功能描述]

要求：
- 遵循 @backend/apps/users 的代码模式
- 使用 @backend/utils 中的通用工具
- 集成 @backend/apps/audit 的日志记录
- 符合 @backend/config 中的配置规范
- 添加适当的权限控制

请提供完整的模型、序列化器、视图和URL配置。
```

### 7. 调试和问题解决

#### 错误分析
```
我遇到了以下错误：
[粘贴错误信息]

相关文件：
@backend/apps/users/views.py
@frontend/src/api/user.ts

请帮助分析错误原因并提供解决方案。
```

#### 性能优化
```
@backend/apps/passwords/views.py 中的密码列表查询性能较慢，
请分析性能瓶颈并提供优化方案：

- 数据库查询优化
- 缓存策略
- 分页改进
- 索引建议
```

### 8. 代码质量提升

#### 代码重构
```
重构 @backend/apps/passwords/models.py 中的PasswordEntry模型：

目标：
- 提高代码可读性
- 优化数据库性能
- 增强数据验证
- 改进错误处理
- 保持向后兼容性
```

#### 文档生成
```
为 @backend/apps/users/serializers.py 生成完整的API文档：

要求：
- 详细的字段说明
- 请求/响应示例
- 错误码说明
- 使用场景描述
- 符合OpenAPI规范
```

### 9. 最佳实践建议

1. **充分利用上下文**：使用 `@文件路径` 引用相关文件
2. **分步骤描述**：将复杂任务分解为具体步骤
3. **保持一致性**：参考现有代码风格和架构模式
4. **安全优先**：在密码管理系统中特别注意安全相关的实现
5. **测试驱动**：为新功能同时生成测试代码
6. **文档同步**：及时更新相关文档和注释

这些指导原则可以帮助你更有效地使用Augment Code来开发密码管理系统项目。


1. 可以向我解释一下关于PasswordEntry和User的权限控制流程吗，PasswordEntry、PasswordEntryGroup、PasswordPermission、User、Role之间的关系
2. 我希望去除掉PasswordEntry模型的is_compromised字段，请同时检查前后端代码，移除该字段相关的代码



有以下内容需要优化
1. 在密码列表页面，仅修改密码按钮的文字修改为“修改密码”，图标请更换为更合适的图标。
2. 在密码列表页面，仅更新信息按钮的文字修改为“更新信息”，图标请更换为更合适的图标。
3. 在密码列表页面，点击修改密码和更新信息按钮均无反应。我希望修改密码会弹出模态框，更新信息会跳转到修改密码信息页面。
4. 新增密码页面的组件布局希望进行调整，调整为一栏布局，将标题、ip地址、用户名、密码等关键信息放置在表单的前边。更新密码信息页面同样。

1. 更新信息功能可参考密码列表页面的编辑功能，请重新实现。
2. 修改密码按钮目前仍不能正常弹出，错误信息如下
passwords.ts:452 getProtocolsApi 原始响应: (6) [Array(2), Array(2), Array(2), Array(2), Array(2), Array(2)]
passwords.ts:516 getPasswordGroupsApi 原始响应: {count: 9, next: null, previous: null, results: Array(9)}
UpdatePasswordOnlyModal.vue:128  [Vue warn]: Failed to resolve component: a-input
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement. 
  at <UpdatePasswordOnlyModal visible=false onUpdate:visible=fn password-entry=null  ... > 
  at <PasswordList onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< null > key="/passwords" > 
  at <KeepAlive key=0 exclude= [] include= [] > 
  at <BaseTransition mode="out-in" appear=true persisted=false  ... > 
  at <Transition key=0 name="fade-slide" appear=""  ... > 
  at <RouterView> 
  at <LayoutContent> 
  at <LayoutContent id="__vben_main_content" content-compact="wide" content-compact-width=1200  ... > 
  at <VbenLayout sidebar-extra-visible=false onUpdate:sidebarExtraVisible=fn content-compact="wide"  ... > 
  at <BasicLayout onClearPreferencesAndLogout=fn<handleLogout> > 
  at <Basic onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <RouterView> 
  at <AApp> 
  at <Anonymous value= {hashed: true, components: {…}, theme: Theme, token: {…}} > 
  at <ALocaleProvider locale= {locale: 'zh-cn', Pagination: {…}, DatePicker: {…}, TimePicker: {…}, Calendar: {…}, …} ANT_MARK__="internalMark" > 
  at <LocaleReceiver children=fn<children> > 
  at <AConfigProvider locale= {locale: 'zh-cn', Pagination: {…}, DatePicker: {…}, TimePicker: {…}, Calendar: {…}, …} theme= {algorithm: Array(1), token: Proxy(Object)} > 
  at <App>


很好，现在让我们一起继续优化：
1.  更新信息按钮应该是打开一个新的tab页，页面布局可参考新增密码表单，但不要有输入密码部分。
2. UpdatePasswordOnlyModal中，生成密码选项应为预定义好的密码策略，PasswordPolicy已在后端定义，生成密码成功后应同时填充至新密码和密码确认框，同时显示为明文，请完善相关代码。
3. 新增密码页面的穿梭框能否支持输入筛选功能
4.密码列表页面每个密码条目前新增选择框，并支持选择全部功能，新增按钮批量删除密码和批量分享密码，可以暂不实现批量删除密码和批量分享密码功能，确认页面布局效果后再进行编码
5.密码列表页面删除密码应修改为移动到回收站，新增回收站页面，支持恢复和彻底删除功能。

Please add the following 5 enhancement tasks to the TaskList for the password management system:

Please add the following 5 enhancement tasks to the TaskList for the password management system:
1. 密码列表页面每页显示多少对象不生效
2. 密码列表页面批量选择工具栏的取消选择按钮不明显，请更换为更明显的样式

1. 作为一个密码管理项目，但是在项目中使用密码等字眼有些过于敏感，容易引起安全等级提升等不必要麻烦，你可以给出一些项目名称上的建议吗
1. 修改登录页和首页<span class="text-foreground truncate text-nowrap font-semibold">Vben Admin Antd</span>中的Vben Admin Antd文字更改为保险箱

Please add the following5 enhancement tasks to the TaskList for the password management system, and remove completed tasks from TaskList:
1. 密码列表页面的删除密码按钮显示文字应修改为移动到回收站，并添加确认弹框。
2.回收站页面的删除时间和删除者都显示未知，需进行修复
3.回收站页面的恢复按钮也添加确认弹框。
4.密码列表页面的显示详情按钮取消，用户通过点击标题进入到密码详情页面
5.更新信息按钮点击后复用http://localhost:5666/passwords/d3599545-c632-44db-9fb0-f288f7fd9dae/edit页面
6.修改登录页和首页<span class="text-foreground truncate text-nowrap font-semibold">Vben Admin Antd</span>中的Vben Admin Antd文字更改为Digital Vault


权限检查流程
直接所有权：用户拥有的密码条目（owner字段）
组权限访问：通过GroupPermission获得密码组权限，进而访问组内密码条目
权限级别：view(1) < edit(2) < manage(3) < admin(4)
📋 实体关系
User ↔ PasswordEntry：直接所有权关系
User ↔ GroupPermission ↔ PasswordEntryGroup：组权限关系
PasswordEntry ↔ PasswordEntryGroup：多对多关系（通过中间表）
User ↔ Role/Department/Team：组织架构关系

Please add the following enhancement tasks to the TaskList for the password management system, and remove completed tasks from TaskList:
1. 请从易用性和用户操作习惯角度，说明密码授权具体实现过程，具体到哪个页面，采用何种组件，在得到用户确认前，先不要采取任何编码。
2. 将密码组从系统管理菜单中移动至密码管理菜单中
3. 移除前端密码生成器页面
4. 密码列表页面分页器选择每页显示对象个数不生效，请修复
5. 密码列表页面已收藏的密码在操作列的收藏按钮icon应显示为黄色实心五角星，未收藏的密码显示为灰色空心五角星
6. 请将密码列表页面的批量删除按钮修改为移动到回收站
7. 实现密码列表页面的批量移动到回收站功能
8. 在密码列表页面和密码详情页面新增分享按钮，可以将密码分享给其他用户，分享按钮文字为分享，按钮点击后弹出模态框，包含以下内容：
    1. 分享类型：选择分享给用户或生成一次性链接
    2. 分享给用户：选择用户，可以搜索用户
    3. 生成一次性链接：生成链接，点击后自动失效
    4. 分享有效期：默认7天，可自定义
    5. 分享权限：默认只读，可选择可编辑
    6. 确认分享按钮
9. 新增密码分享页面，展示所有分享给我的密码和我分享出去的密码，包含以下内容：
    1. 密码标题
    2. 分享对象
    3. 分享时间
    4. 分享有效期
    5. 分享人
    6. 查看人
    6. 分享权限
    7. 操作：查看、删除


1. 在新增密码和修改密码界面，在修改密码组组件旁应增加提示，告知用户密码在添加到密码组后，会被其他人看到或使用，需要谨慎操作。
2. 实现密码列表页面中批量分享密码功能
3. 对于首次登录或使用默认密码登录的用户，应强制修改密码。
4.#### Password Reset Flow
**Current State**: Backend endpoints exist, no frontend
**Proposed Solution**:
- "Forgot Password" link on login page
- Email verification step
- Secure password reset form
- Success confirmation page
5. 
### 2.3 Security Transparency

#### Session Management
**Proposed Features**:
- Active sessions list in user profile
- Device/location information for sessions
- Remote session termination capability
- Login history with security events

6. #### Password Reset Flow
**Current State**: Backend endpoints exist, no frontend
**Proposed Solution**:
- "Forgot Password" link on login page
- Email verification step
- Secure password reset form
- Success confirmation page

7. 前端页面中菜单栏的组权限管理没有图标



1. 移除密码分享中分享给用户的功能。
2. 在密码分享页面，点击分享密码后，如果链接生成成功，则直接复制密码到粘贴板，并进行提示。
3.密码分享访问页面，新增正在访问someone分享给你的密码
4.如果不存在分享密码给用户功能的话，请将密码管理菜单下密码分享页面移除，并移除相关代码


1. 密码分享页面中需要新增一些密码信息显示，ip地址、端口、数据库类型等信息 ，“密码分享访问”没有什么意义，可以删除。
2. 请补充实现批量移动到回收站功能。

1.新增和编辑用户页面团队采用下拉框的方式进行选择
2.前端页面的菜单和按钮未和用户权限做绑定，请完善相关功能
3.在前端页面完成审计报表页面，在左侧菜单栏单独的菜单
4.在前端页面完成SystemSetting，在系统管理下新增菜单
5.

请使用playwright进行测试，验证审计报表页面和系统设置页面是否可正常使用，如有错误请先认真分析可能原因，在进行代码修正



https://drf-spectacular.readthedocs.io/en/latest/readme.html

API Schema下载: http://localhost:8001/api/schema/
Swagger UI界面: http://localhost:8001/api/docs/

1.审计概览页面 数据无法正常显示
:8001/api/audit/stats/?days=30:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
  <h1>FieldError
       at /api/audit/stats/</h1>
  <pre class="exception_value">Cannot resolve keyword &#x27;timestamp&#x27; into field. Choices are: affected_resources, assigned_to, assigned_to_id, created_at, description, event_data, event_type, id, resolution_notes, resolved_at, severity, status, title, updated_at, user, user_id</pre>
  <table class="meta">
2.操作日志界面的按钮ui需要优化，按钮图标和文字需要居中，另外按钮有些太长了
3.导出日志按钮点击报错 request-client.ts:136 
 
 POST http://localhost:8001/api/audit/export/ 500 (Internal Server Error)
operation-logs.vue:387 
 导出日志失败: 
Blob {size: 125562, type: 'text/html'}
4.安全日志的界面问题和操作日志界面一样，请修复一下
5.系统设置页面报错 index.vue:191 
 Uncaught (in promise) TypeError: settings.value.filter is not a function
    at ComputedRefImpl.fn (index.vue:191:25)
    at index.vue:43:34
    at Proxy._sfc_render (PermissionGuard.vue:3:5)

6.目前左侧有两个系统管理菜单，请合并到一起。
以上任务编码前请先查看api文档，完成编码后请通过playwright完成测试


1PasswordEntry需要新增两个字段，分别是SYSTEM_TYPE和MDW_TYPE，CHOICES我已在代码中进行添加，请添加字段，并执行migrate.
2PasswordEntry新增ip地址字段
3. 我想优化一下密码管理的流程。业务流程如下：创建密码时首先选择system_type，并根据以下逻辑控制组件显示：
1如果system_type是os,则表单顺序为 system_type,OS_TYPE_CHOICES,ip,username,password,confirm_password,project_name,environment,notes
2如果system_type是mdw,则表单顺序为 system_type,MDW_TYPE_CHOICES,ip,username,password,confirm_password,port,project_name,environment,notes
3如果system_type是db,则表单顺序为 system_type,DATABASE_TYPE_CHOICES,ip,username,password,confirm_password,port,database_name,project_name,environment,notes
4如果system_type是ftp或sftp,则表单顺序为 system_type,ip,port(如果是ftp则默认填充21，如果是sftp则默认填充22),username,password,confirm_password,database_name,project_name,environment,notes
4如果system_type是网站,则表单顺序为system_type,url,username,password,confirm_password,project_name,environment,notes

    SYSTEM_TYPE_CHOICES = [
        ("os", _("操作系统")),
        ("database", _("数据库")),
        ("middleware", _("中间件")),
        ("ftp", _("FTP")),
        ("sftp", _("SFTP")),
        # ("cloud", _("云服务")),
        # ("application", _("应用系统")),
        ("website", _("网站")),
        ("network", _("网络设备")),
        ("other", _("其他")),
    ]
