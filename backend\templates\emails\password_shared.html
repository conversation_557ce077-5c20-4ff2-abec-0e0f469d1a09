<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码分享通知</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 24px;
        }
        .content {
            margin-bottom: 30px;
        }
        .password-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .password-info h3 {
            margin-top: 0;
            color: #007bff;
        }
        .info-item {
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            display: inline-block;
            width: 120px;
        }
        .info-value {
            color: #6c757d;
        }
        .permission-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .permission-read {
            background-color: #d4edda;
            color: #155724;
        }
        .permission-write {
            background-color: #fff3cd;
            color: #856404;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning h4 {
            color: #856404;
            margin-top: 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 密码分享通知</h1>
        </div>
        
        <div class="content">
            <p>您好，<strong>{{ shared_with.get_full_name }}</strong>！</p>
            
            <p><strong>{{ shared_by.get_full_name }}</strong> 与您分享了一个密码条目。</p>
            
            <div class="password-info">
                <h3>📋 分享详情</h3>
                
                <div class="info-item">
                    <span class="info-label">密码标题：</span>
                    <span class="info-value">{{ password_entry.title }}</span>
                </div>
                
                {% if password_entry.username %}
                <div class="info-item">
                    <span class="info-label">用户名：</span>
                    <span class="info-value">{{ password_entry.username }}</span>
                </div>
                {% endif %}
                
                {% if password_entry.url %}
                <div class="info-item">
                    <span class="info-label">网站地址：</span>
                    <span class="info-value">{{ password_entry.url }}</span>
                </div>
                {% endif %}
                
                <div class="info-item">
                    <span class="info-label">权限级别：</span>
                    <span class="permission-badge {% if permission_level == '只读' %}permission-read{% else %}permission-write{% endif %}">
                        {{ permission_level }}
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">分享时间：</span>
                    <span class="info-value">{{ shared_password.created_at|date:"Y年m月d日 H:i" }}</span>
                </div>
                
                {% if expires_at %}
                <div class="info-item">
                    <span class="info-label">过期时间：</span>
                    <span class="info-value">{{ expires_at|date:"Y年m月d日 H:i" }}</span>
                </div>
                {% endif %}
            </div>
            
            <div class="warning">
                <h4>⚠️ 安全提醒</h4>
                <ul>
                    <li>请妥善保管分享的密码信息</li>
                    <li>不要将密码信息转发给其他人</li>
                    <li>建议在安全的环境下查看密码</li>
                    {% if expires_at %}
                    <li>此分享将在 {{ expires_at|date:"Y年m月d日 H:i" }} 过期</li>
                    {% endif %}
                </ul>
            </div>
            
            <p>您可以登录系统查看完整的密码信息。</p>
        </div>
        
        <div class="footer">
            <p>此邮件由 Locker 密码管理系统自动发送，请勿回复。</p>
            <p>如果您没有请求此分享，请联系系统管理员。</p>
        </div>
    </div>
</body>
</html>