from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import OneTimeLink
from apps.passwords.models import PasswordEntry
from apps.passwords.serializers import PasswordEntrySerializer
from utils.encryption import decrypt_password

User = get_user_model()


class ShareLinkSerializer(serializers.ModelSerializer):
    """分享链接序列化器"""

    created_by_name = serializers.CharField(
        source="created_by.get_full_name", read_only=True
    )
    password_entry_title = serializers.Char<PERSON>ield(
        source="password_entry.title", read_only=True
    )
    is_expired = serializers.SerializerMethodField()
    share_url = serializers.SerializerMethodField()

    class Meta:
        model = OneTimeLink
        fields = [
            "id",
            "password_entry",
            "token",
            "expires_at",
            "require_password",
            "access_password",
            "max_access_count",
            "access_count",
            "status",
            "created_at",
            "accessed_at",
            "created_by_name",
            "password_entry_title",
            "is_expired",
            "share_url",
        ]
        read_only_fields = [
            "id",
            "token",
            "created_by",
            "created_at",
            "accessed_at",
            "access_count",
        ]
        extra_kwargs = {"access_password": {"write_only": True}}

    def get_is_expired(self, obj):
        """检查是否过期"""
        return obj.is_expired

    def get_share_url(self, obj):
        """获取分享链接"""
        from django.conf import settings

        # 获取前端基础URL，优先使用环境变量配置
        frontend_base_url = getattr(
            settings, "FRONTEND_BASE_URL", "http://localhost:5666"
        )

        # 如果有请求上下文，尝试从请求头获取前端URL
        request = self.context.get("request")
        if request:
            # 检查是否有前端Origin头
            origin = request.META.get("HTTP_ORIGIN")
            if origin and (
                "localhost:5666" in origin
                or "localhost:5667" in origin
                or "localhost:5668" in origin
            ):
                frontend_base_url = origin

        return f"{frontend_base_url}/share/{obj.token}/"

    def validate_expires_at(self, value):
        """验证过期时间"""
        if value and value <= timezone.now():
            raise serializers.ValidationError("过期时间必须是未来时间")
        return value

    def create(self, validated_data):
        """创建分享链接时自动生成token"""
        import secrets

        # 生成唯一的token
        while True:
            token = secrets.token_urlsafe(8)
            if not OneTimeLink.objects.filter(token=token).exists():
                break

        validated_data["token"] = token
        return super().create(validated_data)


class ShareLinkAccessSerializer(serializers.Serializer):
    """分享链接访问序列化器"""

    access_password = serializers.CharField(
        required=False, allow_blank=True, help_text="访问密码（如果需要）"
    )

    def validate(self, attrs):
        """验证访问权限"""
        share_link = self.context.get("share_link")

        if not share_link:
            raise serializers.ValidationError("分享链接不存在")

        # 检查链接是否有效
        if not share_link.is_valid:
            raise serializers.ValidationError("分享链接已失效")

        if share_link.is_expired:
            raise serializers.ValidationError("分享链接已过期")

        # 检查访问密码
        if share_link.require_password:
            access_password = attrs.get("access_password")
            if not access_password:
                raise serializers.ValidationError(
                    {"access_password": ["需要提供访问密码"]}
                )

            if share_link.access_password != access_password:
                raise serializers.ValidationError({"access_password": ["访问密码错误"]})

        return attrs


class ShareLinkPasswordSerializer(serializers.Serializer):
    """分享链接密码序列化器"""

    title = serializers.CharField(read_only=True)
    username = serializers.CharField(read_only=True)
    password = serializers.CharField(read_only=True)
    url = serializers.CharField(read_only=True)
    notes = serializers.CharField(read_only=True)

    def to_representation(self, instance):
        """自定义序列化输出"""
        try:
            decrypted_password = decrypt_password(instance.password)
        except Exception:
            decrypted_password = None

        return {
            "title": instance.title,
            "username": instance.username,
            "password": decrypted_password,
            "url": instance.url,
            "notes": instance.notes,
        }


class AuthenticatedShareLinkAccessSerializer(serializers.Serializer):
    """已认证用户分享链接访问序列化器"""

    def validate(self, attrs):
        """验证访问权限（已认证用户跳过密码验证）"""
        share_link = self.context.get("share_link")
        request = self.context.get("request")

        if not share_link:
            raise serializers.ValidationError("分享链接不存在")

        # 检查链接是否有效
        if not share_link.is_valid:
            raise serializers.ValidationError("分享链接已失效")

        if share_link.is_expired:
            raise serializers.ValidationError("分享链接已过期")

        # 已认证用户无需验证访问密码
        if not request or not request.user.is_authenticated:
            raise serializers.ValidationError("需要登录才能访问")

        return attrs
