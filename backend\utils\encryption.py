import base64
import hashlib
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
from django.utils.encoding import force_bytes
import secrets
import string
import os
from gmssl import sm4


class SM4Encryption:
    """
    国密SM4加密工具类
    """

    @staticmethod
    def generate_key(password: str, salt: bytes = None) -> bytes:
        """
        基于密码生成SM4密钥（128位）
        """
        if salt is None:
            salt = settings.SECRET_KEY[:16].encode()

        # 使用PBKDF2生成128位密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=16,  # SM4需要128位（16字节）密钥
            salt=salt,
            iterations=100000,
        )
        key = kdf.derive(force_bytes(password))
        return key

    @staticmethod
    def encrypt_data(data: str, master_key: str) -> str:
        """
        使用SM4加密数据
        """
        key = SM4Encryption.generate_key(master_key)

        # 创建SM4加密器
        sm4_crypt = sm4.CryptSM4()
        sm4_crypt.set_key(key, sm4.SM4_ENCRYPT)

        # 数据填充到16字节的倍数
        data_bytes = data.encode("utf-8")
        padding_length = 16 - (len(data_bytes) % 16)
        padded_data = data_bytes + bytes([padding_length] * padding_length)

        # 加密 - 使用完整数据一次性加密
        encrypted_data = sm4_crypt.crypt_ecb(padded_data)

        return base64.b64encode(encrypted_data).decode("utf-8")

    @staticmethod
    def decrypt_data(encrypted_data: str, master_key: str) -> str:
        """
        使用SM4解密数据
        """
        try:
            key = SM4Encryption.generate_key(master_key)

            # 创建SM4解密器
            sm4_crypt = sm4.CryptSM4()
            sm4_crypt.set_key(key, sm4.SM4_DECRYPT)

            # 解码base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode("utf-8"))

            # 解密 - 使用完整数据一次性解密
            decrypted_data = sm4_crypt.crypt_ecb(encrypted_bytes)

            # 去除填充
            if len(decrypted_data) > 0:
                padding_length = decrypted_data[-1]
                if padding_length <= 16 and padding_length > 0:
                    original_data = decrypted_data[:-padding_length]
                else:
                    original_data = decrypted_data
            else:
                original_data = decrypted_data

            return original_data.decode("utf-8")
        except Exception as e:
            raise ValueError(f"SM4解密失败: {str(e)}")


class PasswordEncryption:
    """
    密码加密工具类
    """

    @staticmethod
    def generate_key(password: str, salt: bytes = None) -> bytes:
        """
        基于密码生成加密密钥
        """
        if salt is None:
            salt = settings.SECRET_KEY[:16].encode()

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(force_bytes(password)))
        return key

    @staticmethod
    def encrypt_password(password: str, master_key: str) -> str:
        """
        加密密码
        """
        key = PasswordEncryption.generate_key(master_key)
        f = Fernet(key)
        encrypted_password = f.encrypt(force_bytes(password))
        return base64.urlsafe_b64encode(encrypted_password).decode()

    @staticmethod
    def decrypt_password(encrypted_password: str, master_key: str) -> str:
        """
        解密密码
        """
        try:
            key = PasswordEncryption.generate_key(master_key)
            f = Fernet(key)
            encrypted_data = base64.urlsafe_b64decode(encrypted_password.encode())
            decrypted_password = f.decrypt(encrypted_data)
            return decrypted_password.decode()
        except Exception:
            raise ValueError("密码解密失败，请检查主密码是否正确")

    @staticmethod
    def generate_random_password(
        length: int = 16,
        include_uppercase: bool = True,
        include_lowercase: bool = True,
        include_digits: bool = True,
        include_symbols: bool = True,
    ) -> str:
        """
        生成随机密码
        """
        characters = ""
        if include_lowercase:
            characters += string.ascii_lowercase
        if include_uppercase:
            characters += string.ascii_uppercase
        if include_digits:
            characters += string.digits
        if include_symbols:
            characters += "!@#$%^&*()_+-=[]{}|;:,.<>?"

        if not characters:
            raise ValueError("至少需要选择一种字符类型")

        password = "".join(secrets.choice(characters) for _ in range(length))
        return password

    @staticmethod
    def hash_master_password(password: str) -> str:
        """
        对主密码进行哈希处理
        """
        salt = settings.SECRET_KEY.encode()
        return hashlib.pbkdf2_hmac("sha256", password.encode(), salt, 100000).hex()

    @staticmethod
    def verify_master_password(password: str, hashed_password: str) -> bool:
        """
        验证主密码
        """
        return PasswordEncryption.hash_master_password(password) == hashed_password


def encrypt_data(data: str, key: str = None) -> str:
    """
    通用数据加密函数 - 使用国密SM4算法
    """
    if key is None:
        key = settings.SECRET_KEY
    return SM4Encryption.encrypt_data(data, key)


def decrypt_data(encrypted_data: str, key: str = None) -> str:
    """
    通用数据解密函数 - 使用国密SM4算法
    """
    if key is None:
        key = settings.SECRET_KEY
    return SM4Encryption.decrypt_data(encrypted_data, key)


# 为了兼容serializers.py中的导入，添加别名
decrypt_password = decrypt_data
encrypt_password = encrypt_data
