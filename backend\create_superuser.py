#!/usr/bin/env python
import os
import sys
import django

# 添加项目路径到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# 初始化Django
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

# 创建超级用户
if not User.objects.filter(username='admin').exists():
    user = User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123'
    )
    print(f'超级用户 {user.username} 创建成功！')
    print('用户名: admin')
    print('密码: admin123')
else:
    print('超级用户 admin 已存在')