from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
import uuid
from datetime import timedelta
from django.utils import timezone

User = get_user_model()


class OneTimeLink(models.Model):
    """一次性链接模型"""

    LINK_STATUS = [
        ("active", _("活跃")),
        ("used", _("已使用")),
        ("expired", _("已过期")),
        ("revoked", _("已撤销")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    token = models.CharField(max_length=64, unique=True, verbose_name=_("访问令牌"))
    password_entry = models.ForeignKey(
        "passwords.PasswordEntry",
        on_delete=models.CASCADE,
        related_name="onetime_links",
        verbose_name=_("密码条目"),
    )
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, verbose_name=_("创建者")
    )

    # 链接设置
    status = models.CharField(
        max_length=10, choices=LINK_STATUS, default="active", verbose_name=_("状态")
    )
    expires_at = models.DateTimeField(verbose_name=_("过期时间"))
    require_password = models.BooleanField(default=False, verbose_name=_("需要密码"))
    access_password = models.CharField(
        max_length=128, blank=True, verbose_name=_("访问密码")
    )
    max_access_count = models.IntegerField(
        null=True, blank=True, verbose_name=_("最大访问次数")
    )
    access_count = models.IntegerField(default=0, verbose_name=_("访问次数"))

    # 访问信息
    accessed_at = models.DateTimeField(
        null=True, blank=True, verbose_name=_("访问时间")
    )
    accessed_ip = models.GenericIPAddressField(
        null=True, blank=True, verbose_name=_("访问IP")
    )
    accessed_user_agent = models.TextField(blank=True, verbose_name=_("访问用户代理"))

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))

    class Meta:
        verbose_name = _("一次性链接")
        verbose_name_plural = _("一次性链接")
        db_table = "onetime_links"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.password_entry.title} - {self.token[:8]}..."

    @property
    def is_expired(self):
        """检查链接是否过期"""
        # 检查时间过期
        if timezone.now() > self.expires_at:
            return True
        # 检查访问次数过期
        if self.max_access_count and self.access_count >= self.max_access_count:
            return True
        return False

    @property
    def is_valid(self):
        """检查链接是否有效"""
        return self.status == "active" and not self.is_expired

    def mark_as_used(self, ip_address=None, user_agent=None):
        """标记为已使用"""
        # 增加访问次数
        self.access_count += 1
        self.accessed_at = timezone.now()
        if ip_address:
            self.accessed_ip = ip_address
        if user_agent:
            self.accessed_user_agent = user_agent

        # 检查是否达到最大访问次数
        if self.max_access_count and self.access_count >= self.max_access_count:
            self.status = "used"

        self.save()

    def save(self, *args, **kwargs):
        if not self.token:
            import secrets

            self.token = secrets.token_urlsafe(32)

        # 如果没有设置过期时间，默认24小时后过期
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(hours=24)

        super().save(*args, **kwargs)


# 为了兼容views.py中的导入，添加别名
ShareLink = OneTimeLink


class ShareAccessLog(models.Model):
    """分享访问日志模型"""

    ACTION_TYPES = [
        ("view", _("查看")),
        ("copy_username", _("复制用户名")),
        ("copy_password", _("复制密码")),
        ("copy_url", _("复制网址")),
        ("download_attachment", _("下载附件")),
    ]

    onetime_link = models.ForeignKey(
        OneTimeLink,
        on_delete=models.CASCADE,
        related_name="access_logs",
        null=True,
        blank=True,
        verbose_name=_("一次性链接"),
    )

    action_type = models.CharField(
        max_length=20, choices=ACTION_TYPES, verbose_name=_("操作类型")
    )

    # 访问信息
    ip_address = models.GenericIPAddressField(verbose_name=_("IP地址"))
    user_agent = models.TextField(blank=True, verbose_name=_("用户代理"))
    accessed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("访问用户"),
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("访问时间"))

    class Meta:
        verbose_name = _("分享访问日志")
        verbose_name_plural = _("分享访问日志")
        db_table = "share_access_logs"
        ordering = ["-created_at"]

    def __str__(self):
        if self.onetime_link:
            return f"{self.onetime_link.password_entry.title} - {self.action_type}"
        return f"Unknown - {self.action_type}"
