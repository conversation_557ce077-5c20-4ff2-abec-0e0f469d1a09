[{"description": "API文档", "method": "GET", "url": "/api/docs/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "用户资料", "method": "GET", "url": "/api/auth/profile/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "用户列表", "method": "GET", "url": "/api/auth/users/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "部门列表", "method": "GET", "url": "/api/auth/departments/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "团队列表", "method": "GET", "url": "/api/auth/teams/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "角色列表", "method": "GET", "url": "/api/auth/roles/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "密码条目列表", "method": "GET", "url": "/api/passwords/entries/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "分类列表", "method": "GET", "url": "/api/passwords/categories/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "标签列表", "method": "GET", "url": "/api/passwords/tags/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "密码生成器", "method": "POST", "url": "/api/passwords/generator/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "安全分析", "method": "GET", "url": "/api/passwords/security-analysis/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "共享密码列表", "method": "GET", "url": "/api/sharing/shared-passwords/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "接收的共享密码", "method": "GET", "url": "/api/sharing/received-passwords/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "分享链接列表", "method": "GET", "url": "/api/sharing/share-links/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "操作日志", "method": "GET", "url": "/api/audit/operation-logs/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "访问日志", "method": "GET", "url": "/api/audit/access-logs/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "安全事件", "method": "GET", "url": "/api/audit/security-events/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "审计统计", "method": "GET", "url": "/api/audit/stats/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "用户活动", "method": "GET", "url": "/api/audit/user-activity/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "系统设置", "method": "GET", "url": "/api/system/settings/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "邮件模板", "method": "GET", "url": "/api/system/email-templates/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "备份配置", "method": "GET", "url": "/api/system/backup-configs/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}, {"description": "系统状态", "method": "GET", "url": "/api/system/status/", "success": false, "result": "cannot import name 'encrypt_data' from 'utils.encryption' (D:\\dev\\locker\\backend\\utils\\encryption.py)"}]